import glob
import yagmail
import time
import shutil

'''
当前文件夹下存在公报数据excel文件都是那个公报日的
'''
def days_of_excel_files():
    files = glob.glob('*_????-??-??.xlsx')
    days = []
    for f in files:
         days.append(f.split('.')[0][-10:])
    days = sorted(set(days), key=days.index)
    return days

'''
是否存在当天的公报数据excel文件
'''
#对应日期的法律状态excel文件列表
def is_have_legal_excel(date):
    pattern = '*' + '法律状态_' + date + '.xlsx'
    # print(pattern)
    files1 = glob.glob(pattern)
    files2 = glob.glob('*宣告专利权部分无效审查结论公告*.xlsx') + glob.glob('*宣告专利权部分无效审查结论公告*.zip')
    files = files1
    # print(files)
    if len(files) > 0:
        return files
    else:
        return None

'''
发送邮件
'''
def send_mail(to_addr, date):
    #生成附件列表
    files = is_have_legal_excel(date)
    if len(files) >= 3:
        files_path = ['.\\' + x for x in files]
    else:
        raise ValueError('文件数量不足三个，是否异常请检查')

    #构造连接
    yag = yagmail.SMTP(
                    #    user="<EMAIL>",
                       user="<EMAIL>",
                       password="jMN3jeDx8BaN2nx4",
                       host='smtp.exmail.qq.com')
   
    #发送邮件
    try:
        file_names = '\n'.join(files)
        yag.send(bcc=to_addr,
                 subject=f'中国专利法律状态更新数据：{date}',
                 contents=f'您好，\n    附件是{date}的中国专利法律状态更新数据文件，文件名如下：\n {file_names}',
                 attachments=files_path)
        print('{0}的数据文件：{1}，\n已发送给：{2}'.format(date, '、'.join(files), '、'.join(to_addr)))
    except Exception as e:
        print(e)

    #关闭连接
    yag.close()

'''
处理完的文件移入“按公开日汇总”
'''
def clean_file(date):
    files = []
    if is_have_legal_excel(date):
        files = is_have_legal_excel(date)
    for f in files:
        shutil.move(f, ".\\按公开日汇总\\")


if __name__ == "__main__":
    today = time.strftime("%Y-%m-%d", time.localtime())
    # print(today)
    # today = '2021-05-07'  #测试
    to_addr = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    to_myself = []
    days = days_of_excel_files()
    if is_have_legal_excel(today):
        print(f'今天是{today}，开始发送今天的公报excel文件')
        send_mail(to_addr, today)
        # send_mail(to_myself, today)
        clean_file(today)
    else:
        print('今天是{1}，没有今天的公报excel文件，开始发送本地该目录下其他日期{0}的数据文件:'.format('、'.join(days), today))
        if len(days) == 0:
            print('本地目录下没有可发送的公报数据Excel文件，任务结束')
        else:
            for day in days:
                send_mail(to_addr + to_myself, day)
                clean_file(day)