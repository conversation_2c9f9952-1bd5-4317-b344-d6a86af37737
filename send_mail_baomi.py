import glob
# from logging import raiseExceptions
import yagmail
import time
# import os



#对应日期的保密专利excel文件列表
def is_have_secret_excel(date):
    d = date.split('-')
    date = str(int(d[0])) + '-' + str(int(d[1])) + '-' + str(int(d[2]))
    pattern = '保密' + '*' + '专利授权&解密' + '*_' + date + '.xlsx'

    files_FM = glob.glob('.\\保密专利\\保密发明专利Excel数据\\' + pattern)
    files_XX = glob.glob('.\\保密专利\\保密新型专利Excel数据\\' + pattern)
    files = files_FM + files_XX
    files = [x.split('\\')[-1] for x in files ]
    # print(files)

    files_path = []
    for f in files:
        if '发明' in f:
            files_path.append('.\\保密专利\\保密发明专利Excel数据\\' + f)
        elif '新型' in f:
            files_path.append('.\\保密专利\\保密新型专利Excel数据\\' + f)
    # print(files_path)

    if len(files) > 0:
        return files, files_path
    else:
        return None, None

def send_mail(to_addr, date):
    
    #生成附件列表
    f = is_have_secret_excel(date)
    # print(f)
    files = f[0]
    files_path = f[1]

    # print('、'.join(files))
    # print(files_path)
    # return 
    

    #构造连接
    yag = yagmail.SMTP(
                        # user="<EMAIL>",
                        user="<EMAIL>",
                       password="jMN3jeDx8BaN2nx4",
                       host='smtp.exmail.qq.com')
   
    #发送邮件
    try:
        file_names = '\n'.join(files)
        yag.send(bcc=to_addr,
                 subject=f'/保/密/专利法律状态更新数据：{date}',
                 contents=f'您好，\n    附件是{date}的保mi专利法律状态更新数据文件，文件名如下：\n{file_names}',
                 attachments=files_path)
        print('{0}的数据文件：{1}，\n已发送给：{2}'.format(date, '、'.join(files), '、'.join(to_addr)))
    except Exception as e:
        print(e)

    #关闭连接
    yag.close()


if __name__ == "__main__":
    today = time.strftime("%Y-%m-%d", time.localtime())
    # print(today)
    
    #日期开关，过期补发
    # today = '2024-04-04'  #测试

    to_addr = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    to_myself = ['<EMAIL>']
 
    if is_have_secret_excel(today)[0]:
        print(f'今天是{today}，开始发送今天的公报excel文件')
        send_mail(to_addr + to_myself, today)
    else:
        print(f'今天是{today}，没有今天的公报excel文件，结束任务')
    

