import glob
# from token import ENCODING
# from logging import raiseExceptions
import yagmail
import time
import os
import shutil



#对应日期的保密专利excel文件列表
def is_have_gengzheng_excel(date):
    d = date.split('-')
    # date = str(int(d[0])) + '-' + str(int(d[1])) + '-' + str(int(d[2]))
    pattern = '*' + '专利更正*' + '_' + date + '.xlsx'

    files = glob.glob(pattern)
    # files = [x.split(u'\\')[-1] for x in files ]
    # print(date)
    # print(files)

    files_path = ['.\\'+x for x in files]
    # print(files_path)


    if len(files) > 0:
        return files, files_path
    else:
        return None, None

def send_mail(to_addr, date):
    #生成附件列表
    f = is_have_gengzheng_excel(date)
    # print(f)
    files = f[0]
    files_path = f[1]
    

    #构造连接
    yag = yagmail.SMTP(
                        # user="<EMAIL>",
                        user="dong<PERSON><PERSON>@bjzykjyxgs53.wecom.work",
                       password="jMN3jeDx8BaN2nx4",
                       host='smtp.exmail.qq.com')
   
    #发送邮件
    try:
        file_names = '\n'.join(files)
        yag.send(bcc=to_addr,
                 subject=f'中国专利更正更新数据：{date}',
                 contents=f'您好，\n    附件是{date}的中国专利更正更新数据文件，文件名如下：\n{file_names}',
                 attachments=files_path)
        print('{0}的数据文件：{1}，\n已发送给：{2}'.format(date, '、'.join(files), '、'.join(to_addr)))
    except Exception as e:
        print(e)

    #关闭连接
    yag.close()

'''
处理完的文件移入“按公开日汇总”
'''
def clean_file(date):
    # files = []
    if is_have_gengzheng_excel(date):
        files = is_have_gengzheng_excel(date)[0]
        # print(files)
    else:
        print('待发送文件为空')
    for f in files:
        if not os.path.exists(date[:4]+'Excel格式更正数据'): 
            os.mkdir(date[:4]+'Excel格式更正数据')
        shutil.move(f, f".\\{date[0:4]}Excel格式更正数据\\")

'''
当前文件夹下存在公报数据excel文件都是那个公报日的
'''
def days_of_excel_files():
    files = glob.glob('*_????-??-??.xlsx')
    days = []
    for f in files:
         days.append(f.split('.')[0][-10:])
    days = sorted(set(days), key=days.index)
    return days


if __name__ == "__main__":
    today = time.strftime("%Y-%m-%d", time.localtime())
    # print(today)

    #日期开关，补发
    # today = '2024-06-07'  #测试

    to_addr = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    to_myself = ['<EMAIL>']

    os.chdir('.\\更正\\')
    days = days_of_excel_files()
    # print(days)
    if is_have_gengzheng_excel(today)[0]:
        print(f'今天是{today}，开始发送今天的更正公报excel文件')
        send_mail(to_addr + to_myself, today)
        clean_file(today)
    # else:
    #     print(f'今天是{today}，没有今天的更正公报excel文件，结束任务')
    else:
        print('今天是{1}，没有今天的公报excel文件，开始发送本地该目录下其他日期{0}的数据文件:'.format('、'.join(days), today))
        if len(days) == 0:
            print('没有可发送的公报数据Excel文件，任务结束')
        else:
            for day in days:
                send_mail(to_addr + to_myself, day)
                # print('模拟发信')
                clean_file(day)
   
    os.chdir('..')

