import asyncio
import sys
import gc
import time
from pathlib import Path
from playwright.async_api import async_playwright, Browser
from playwright import _repo_version
from corutine_config import config
from corutine_spider import (
    context_init,
    query_patent_info_from_web,
    get_context_be_logined,
)

# for dev(2025.7.11)
from queue_scheduling_v2 import gen_task_queue


# from corutine_queue_scheduling import (
#     gen_task_queue,
#     # result_queue_save_to_Mysql
# )

from corutine_utility import get_logger
from web_asset_to_oss import StaticProcessor
from result_processor import ResultQueueProcessor

# 常量定义
WORKER_START_DELAY = 10  # 工作协程启动间隔（秒）
RESULT_QUEUE_SIZE = 5000  # 结果队列大小
TASK_QUEUE_BUFFER = 1000  # 任务队列缓冲区大小
FINAL_WAIT_TIME = 30  # 最终等待时间（秒）

# 错误消息常量
ERROR_BROWSER_NOT_FOUND = "！！！浏览器文件不存在，无法进行浏览器初始化！！！"
ERROR_BROWSER_CLOSED = "Target page, context or browser has been closed"
MSG_TASK_QUEUE_EMPTY = "！！！任务队列已空，继续等待状态：结果队列为空"
MSG_RESULT_QUEUE_EMPTY = "！！！结果队列已空！！！"
MSG_CLEANUP_START = "--将退出本次任务的全部爬取协程--"
MSG_CLEANUP_COMPLETE = "！！！全部爬取协程退出完毕！！！"


def print(*args, **kwargs):
    logger = get_logger(__name__)
    logger.info(*args, **kwargs)


async def _initialize_worker_context(browser: Browser, name: str, device: dict):
    """初始化工作协程的浏览器上下文"""
    context = await context_init(name, browser, device)
    context.set_default_timeout(config.CONTEXT_TIMEOUT)  # type: ignore  # noqa: F405
    return context


async def _login_worker_context(name: str, context):
    """为工作协程执行登录"""
    return await get_context_be_logined(name, context)


async def _handle_browser_closed_error(name: str, queue: asyncio.Queue, error: Exception):
    """处理浏览器关闭错误"""
    print(f"！！！工作协程 {name} 检测到浏览器被关闭：{error}！！！")

    # 清空任务队列，避免其他协程继续等待
    cleared_count = 0
    print(f"工作协程 {name} 开始清空任务队列...")

    try:
        while not queue.empty():
            await queue.get()
            queue.task_done()
            cleared_count += 1

        print(f"工作协程 {name} 已清空 {cleared_count} 个任务")

    except Exception as cleanup_error:
        print(f"工作协程 {name} 清空任务队列时出错：{cleanup_error}")


async def worker_context(
    browser: Browser,
    name: str,
    queue: asyncio.Queue,
    result_queue: asyncio.Queue,
    device: dict,
    static_processor: StaticProcessor,
) -> None:
    """
    每个context一个协程，多context上下文
    重构后的工作协程函数，职责更清晰
    """
    context = None

    try:
        # 上下文初始化
        context = await _initialize_worker_context(browser, name, device)

        # 上下文登录
        context = await _login_worker_context(name, context)

        # 页面查询
        await query_patent_info_from_web(
            context, name, queue, result_queue, static_processor
        )

    except ValueError as e:
        print(f"！！！工作协程 {name} 发现业务逻辑错误：{e}！！！")

    except Exception as e:
        error_msg = str(e)
        if ERROR_BROWSER_CLOSED in error_msg:
            await _handle_browser_closed_error(name, queue, e)
        else:
            print(f"！！！工作协程 {name} 发生未知错误：{error_msg}！！！")
            # 记录详细错误信息用于调试
            import traceback
            print(f"详细错误堆栈：\n{traceback.format_exc()}")
            raise ValueError(f"工作协程 {name} 未定义错误：{error_msg}")

    finally:
        # 确保上下文被正确关闭
        if context:
            try:
                await context.close()
                print(f"工作协程 {name} 上下文已正常关闭")
            except Exception as close_error:
                print(f"关闭工作协程 {name} 上下文时出错：{close_error}")

    return


async def _print_startup_info():
    """打印启动信息"""
    print(f"""cpquery_RPA开始运行，概要信息：
                ---部署角色：{config.ROLE.name}
                ---任务来源：{config.TASK_SOURCE}
                ---程序版本：{config.VERSION}
                ---python版本：{sys.version}
                ---核心库版本：{_repo_version.version}
                ---浏览器文件：{config.BROWSER_PATH}
                ---并发数量：{config.MAX_TASK_NUMBER}""")


async def _initialize_queues():
    """初始化任务队列和结果队列"""
    task_queue = asyncio.Queue(maxsize=config.TASK_QUEUE_MAX_SIZE + TASK_QUEUE_BUFFER)
    result_queue = asyncio.Queue(RESULT_QUEUE_SIZE)
    return task_queue, result_queue


async def _initialize_browser(playwright):
    """初始化浏览器实例"""
    browser_path = Path(config.BROWSER_PATH)
    if not browser_path.exists():
        print(ERROR_BROWSER_NOT_FOUND)
        return None

    try:
        browser = await playwright.chromium.launch(
            headless=config.BROWSER_HEADLESS,
            channel="msedge",
            executable_path=browser_path,
        )
        return browser
    except Exception as e:
        print(f"！！！浏览器启动失败：{e}！！！")
        return None


async def _create_worker_tasks(browser, task_queue, result_queue, static_processor):
    """创建工作协程任务"""
    task_list = []
    for i in range(config.MAX_TASK_NUMBER):
        worker_name = f"worker-{i}"
        print(f"正在启动工作协程：{worker_name}")

        task = asyncio.create_task(
            worker_context(
                browser,
                worker_name,
                task_queue,
                result_queue,
                device=config.IPAD_PRO_11,
                static_processor=static_processor,
            )
        )
        task_list.append(task)

        # 避免同时启动过多协程造成资源竞争
        if i < config.MAX_TASK_NUMBER - 1:  # 最后一个不需要等待
            await asyncio.sleep(WORKER_START_DELAY)

    print(f"已创建 {len(task_list)} 个工作协程")
    return task_list


async def _start_background_tasks(task_queue, result_queue):
    """启动后台任务（队列维护和结果处理）"""
    # 启动任务队列维护任务
    queue_task = asyncio.create_task(gen_task_queue(task_queue, source=config.TASK_SOURCE))

    # 启动结果队列维护任务
    result_processor = ResultQueueProcessor()
    result_task = asyncio.create_task(result_processor.start_scheduled_tasks(result_queue))

    return queue_task, result_task


async def _wait_for_completion(task_queue, result_queue):
    """等待任务完成"""
    try:
        print("等待任务队列完成...")
        await task_queue.join()
        print(MSG_TASK_QUEUE_EMPTY)

        print("等待结果队列完成...")
        await result_queue.join()
        print(MSG_RESULT_QUEUE_EMPTY)

        print(f"等待 {FINAL_WAIT_TIME} 秒确保所有任务完成...")
        await asyncio.sleep(FINAL_WAIT_TIME)

    except Exception as e:
        print(f"！！！等待任务完成时发生错误：{e}，应核实处理！！！")
        raise


async def _cleanup_tasks(task_list):
    """清理所有任务"""
    if not task_list:
        print("没有需要清理的任务")
        return

    print(MSG_CLEANUP_START)

    # 取消所有任务
    for index, task in enumerate(task_list, 1):
        if not task.done():
            print(f"正在退出第 {index}/{len(task_list)} 个协程...")
            task.cancel()
        else:
            print(f"第 {index}/{len(task_list)} 个协程已完成")

    # 等待所有任务完成或被取消
    try:
        await asyncio.gather(*task_list, return_exceptions=True)
    except Exception as e:
        print(f"！！！在取消任务时发生错误：{e}，应核实处理！！！")
    finally:
        print(MSG_CLEANUP_COMPLETE)


async def main():
    """主函数 - 重构后更清晰的结构"""
    try:
        await _print_startup_info()

        # 初始化队列
        task_queue, result_queue = await _initialize_queues()
        print("队列初始化完成")

        async with async_playwright() as playwright:
            # 初始化浏览器
            browser = await _initialize_browser(playwright)
            if browser is None:
                print("浏览器初始化失败，程序退出")
                return

            all_tasks = []
            try:
                # 初始化静态资源处理器
                static_processor = StaticProcessor()
                print("静态资源处理器初始化完成")

                # 设置爬虫运行标志
                config.FLAG_SPIDER_RUNNING = True
                print("爬虫运行标志已设置")

                # 创建工作协程
                worker_tasks = await _create_worker_tasks(browser, task_queue, result_queue, static_processor)

                # 启动后台任务
                queue_task, result_task = await _start_background_tasks(task_queue, result_queue)
                print("后台任务启动完成")

                # 合并所有任务
                all_tasks = worker_tasks + [queue_task, result_task]

                print(f"！任务初始化完成，等待 {config.START_WAIT_TIME} 秒后开始正常运行！")
                await asyncio.sleep(config.START_WAIT_TIME)
                print("！本次任务进入正常运行状态！")

                # 等待任务完成
                await _wait_for_completion(task_queue, result_queue)

            except KeyboardInterrupt:
                print("收到中断信号，正在退出...")
                config.FLAG_SPIDER_RUNNING = False

            except Exception as e:
                print(f"主函数执行过程中发生错误：{e}")
                config.FLAG_SPIDER_RUNNING = False
                raise

            finally:
                # 确保爬虫标志被重置
                config.FLAG_SPIDER_RUNNING = False

                # 清理任务
                if all_tasks:
                    await _cleanup_tasks(all_tasks)

                # 关闭浏览器
                try:
                    await browser.close()
                    print("浏览器已关闭")
                except Exception as e:
                    print(f"关闭浏览器时出错：{e}")

    except Exception as e:
        print(f"主函数发生致命错误：{e}")
        import traceback
        print(f"详细错误信息：\n{traceback.format_exc()}")
        raise


if __name__ == "__main__":
    # asyncio.run(main(), debug=True)
    # os.environ['NODE_OPTIONS'] = '--max-old-space-size=2048'

    if config.RUN_ONCE:
        asyncio.run(main())
    else:
        while True:
            asyncio.run(main())
            print("------------10秒后，换用户登录，再次开启任务------------")
            time.sleep(5)
            gc.collect()
            time.sleep(5)
