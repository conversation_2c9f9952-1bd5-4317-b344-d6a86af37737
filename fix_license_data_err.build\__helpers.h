#ifndef __NUITKA_CALLS_H__
#define __NUITKA_CALLS_H__

extern PyObject *CALL_FUNCTION_WITH_ARGS11(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS12(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS13(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_FUNCTION_WITH_ARGS17(PyThreadState *tstate, PyObject *called, PyObject *const *args) ;
extern PyObject *CALL_METHOD_WITH_ARGS11(PyThreadState *tstate, PyObject *source, PyObject *attr_name, PyObject *const *args) ;
#endif
