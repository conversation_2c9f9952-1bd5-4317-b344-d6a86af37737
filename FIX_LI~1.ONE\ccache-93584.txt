[2025-07-14T09:32:23.365688 93048] === CCACHE 4.8.2 STARTED =========================================
[2025-07-14T09:32:23.365810 93048] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-14T09:32:23.365848 93048] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-14T09:32:23.365848 93048] Config: (default) absolute_paths_in_stderr = false
[2025-07-14T09:32:23.365848 93048] Config: (default) base_dir = 
[2025-07-14T09:32:23.365848 93048] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-14T09:32:23.365848 93048] Config: (default) compiler = 
[2025-07-14T09:32:23.365848 93048] Config: (default) compiler_check = mtime
[2025-07-14T09:32:23.365848 93048] Config: (default) compiler_type = auto
[2025-07-14T09:32:23.365848 93048] Config: (default) compression = true
[2025-07-14T09:32:23.365848 93048] Config: (default) compression_level = 0
[2025-07-14T09:32:23.365848 93048] Config: (default) cpp_extension = 
[2025-07-14T09:32:23.365848 93048] Config: (default) debug = false
[2025-07-14T09:32:23.365848 93048] Config: (default) debug_dir = 
[2025-07-14T09:32:23.365848 93048] Config: (default) depend_mode = false
[2025-07-14T09:32:23.365848 93048] Config: (default) direct_mode = true
[2025-07-14T09:32:23.365848 93048] Config: (default) disable = false
[2025-07-14T09:32:23.365848 93048] Config: (default) extra_files_to_hash = 
[2025-07-14T09:32:23.365848 93048] Config: (default) file_clone = false
[2025-07-14T09:32:23.365848 93048] Config: (default) hard_link = false
[2025-07-14T09:32:23.365848 93048] Config: (default) hash_dir = true
[2025-07-14T09:32:23.365848 93048] Config: (default) ignore_headers_in_manifest = 
[2025-07-14T09:32:23.365848 93048] Config: (default) ignore_options = 
[2025-07-14T09:32:23.365848 93048] Config: (default) inode_cache = true
[2025-07-14T09:32:23.365848 93048] Config: (default) keep_comments_cpp = false
[2025-07-14T09:32:23.365848 93048] Config: (environment) log_file = D:\code\FASTAP~1\FIX_LI~1.ONE\ccache-93584.txt
[2025-07-14T09:32:23.365848 93048] Config: (default) max_files = 0
[2025-07-14T09:32:23.365848 93048] Config: (default) max_size = 5.0 GiB
[2025-07-14T09:32:23.365848 93048] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-14T09:32:23.365848 93048] Config: (default) namespace = 
[2025-07-14T09:32:23.365848 93048] Config: (default) path = 
[2025-07-14T09:32:23.365848 93048] Config: (default) pch_external_checksum = false
[2025-07-14T09:32:23.365848 93048] Config: (default) prefix_command = 
[2025-07-14T09:32:23.365848 93048] Config: (default) prefix_command_cpp = 
[2025-07-14T09:32:23.365848 93048] Config: (default) read_only = false
[2025-07-14T09:32:23.365848 93048] Config: (default) read_only_direct = false
[2025-07-14T09:32:23.365848 93048] Config: (default) recache = false
[2025-07-14T09:32:23.365848 93048] Config: (default) remote_only = false
[2025-07-14T09:32:23.365848 93048] Config: (default) remote_storage = 
[2025-07-14T09:32:23.365848 93048] Config: (default) reshare = false
[2025-07-14T09:32:23.365848 93048] Config: (default) run_second_cpp = true
[2025-07-14T09:32:23.365848 93048] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-14T09:32:23.365848 93048] Config: (default) stats = true
[2025-07-14T09:32:23.365848 93048] Config: (default) stats_log = 
[2025-07-14T09:32:23.365848 93048] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-14T09:32:23.365848 93048] Config: (default) umask = 
[2025-07-14T09:32:23.365941 93048] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o static_src\OnefileBootstrap.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_ONEFILE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\users\qwers\MINICO~1\envs\CPQUER~1\Lib\SITE-P~1\nuitka\build\inline_copy\zlib -I. -ID:\users\qwers\MINICO~1\envs\CPQUER~1\Lib\SITE-P~1\nuitka\build\include -ID:\users\qwers\MINICO~1\envs\CPQUER~1\Lib\SITE-P~1\nuitka\build\static_src -ID:\users\qwers\MINICO~1\envs\CPQUER~1\Lib\SITE-P~1\nuitka\build\inline_copy\zstd static_src\OnefileBootstrap.c
[2025-07-14T09:32:23.370912 93048] Hostname: IBM-R7000P
[2025-07-14T09:32:23.370958 93048] Working directory: D:/code/FASTAP~1/FIX_LI~1.ONE
[2025-07-14T09:32:23.370983 93048] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-07-14T09:32:23.370999 93048] Compiler type: gcc
[2025-07-14T09:32:23.371718 93048] Source file: static_src\OnefileBootstrap.c
[2025-07-14T09:32:23.371753 93048] Object file: static_src\OnefileBootstrap.o
[2025-07-14T09:32:23.372062 93048] Trying direct lookup
[2025-07-14T09:32:23.372383 93048] Manifest key: 8291tda093ltnrbcm01t26nsm9uiaq278
[2025-07-14T09:32:23.382557 93048] Retrieved 8291tda093ltnrbcm01t26nsm9uiaq278 from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/2/91tda093ltnrbcm01t26nsm9uiaq278M)
[2025-07-14T09:32:23.461325 93048] Got result key from manifest
[2025-07-14T09:32:23.461376 93048] Result key: 899ao6g1fj825gu5hogd7dm6ua8mfnpok
[2025-07-14T09:32:23.473056 93048] Retrieved 899ao6g1fj825gu5hogd7dm6ua8mfnpok from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/9/9ao6g1fj825gu5hogd7dm6ua8mfnpokR)
[2025-07-14T09:32:23.474256 93048] Reading embedded entry #0 .o (588043 bytes)
[2025-07-14T09:32:23.474324 93048] Writing to static_src\OnefileBootstrap.o
[2025-07-14T09:32:23.475506 93048] Succeeded getting cached result
[2025-07-14T09:32:23.475660 93048] Result: direct_cache_hit
[2025-07-14T09:32:23.475679 93048] Result: local_storage_hit
[2025-07-14T09:32:23.475694 93048] Result: local_storage_read_hit
[2025-07-14T09:32:23.475708 93048] Result: local_storage_read_hit
[2025-07-14T09:32:23.475730 93048] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
[2025-07-14T09:32:23.476516 93048] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
[2025-07-14T09:32:23.478063 93048] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
[2025-07-14T09:32:23.478192 93048] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
