'''
# @Time    : 2024-04-26
# <AUTHOR> wwind
# @File    : fix_license_data_err.py
# @Software: wwind      
# @Description: 修复事务解析数据excel文件中，关于专利许可合同备案事件数据的格式错误。
# @version: v1.52 (2025.01.17)

# todo:
1, [实用新型名称  一种经济型双屏蔽CAT7网线     能的CAT6  UTP网线]  这种格式的处理，目前逻辑处理不正确

'''

import json
import openpyxl
import pdfplumber
import re
import datetime
import time
import os
import glob
import sys



'''
专利许可合同备案数据样例：
IPC(主分类)  C22C 47/14 
合同备案号  X2021140000003
专利申请号  201611049277.9 申请日  2016.11.25
让与人  太原理工大学
受让人  运城市大正新能源汽车配件有限公司
发明名称  一种WC-TaC-Csf强韧性材料及其制备方法
申请公布日  2017.03.15 
授权公告日  2018.06.12
许可种类  普通许可 
备案日期  2021.03.11
'''

KEYWORD_METADATA = ('IPC(主分类)', '主分类号', '合同备案号', '专利申请号', '专利号', '申请日', '让与人', '受让人', '发明名称', '实用新型名称', '使用外观设计的产品名称', '申请公布日', '授权公告日', '许可种类', '备案日期')
KEYWORD_FORMAT: dict[str, int] = {
                    '分割线' : 0,
                    'IPC(主分类)': 1,
                    '主分类号': 1,
                    '合同备案号': 1,
                    '专利申请号': 2,
                    '专利号': 2,
                    '申请日': 2,
                    '让与人': 3,
                    '受让人': 4,
                    '发明名称': 5,
                    '实用新型名称': 5,
                    '使用外观设计的产品名称': 5,
                    '申请公布日': 6,
                    '授权公告日': 6,
                    '许可种类': 7,
                    '备案日期': 7,
                    }
RARE_CHARACTER_IMAGE_FLAG = False   # 是否存在特殊字符的图片
    

def subscript_char(text:str):
    """
    将字符转换为 Unicode 下标字符
    """

    # 替换 '16-x' 为 '1₆₋ₓ'
    replacements = {
                '0': '₀',
                '1': '₁',
                '2': '₂',
                '3': '₃',
                '4': '₄',
                '5': '₅',
                '6': '₆',
                '7': '₇',
                '8': '₈',
                '9': '₉',
                '-': '₋',
                '+': '₊',
                '=': '₌',
                '(': '₍',
                ')': '₎',
                'a': 'ₐ',
                'e': 'ₑ',
                'h': 'ₕ',
                'i': 'ᵢ',
                'j': 'ⱼ',
                'k': 'ₖ',
                'l': 'ₗ',
                'm': 'ₘ',
                'n': 'ₙ',
                'o': 'ₒ',
                'p': 'ₚ',
                'r': 'ᵣ',
                's': 'ₛ',
                't': 'ₜ',
                'u': 'ᵤ',
                'v': 'ᵥ',
                'x': 'ₓ',
                'y': 'ᵧ',
                'z': '𝓏',
                '*': 'ₓ',  # 使用 x 的下标表示
                '/': '∕',
                '.': '.',  # 使用·表示小数点
            }

        # 将指定字符转换为下标字符
        
    result = ""
    for char in text:
        if char in replacements:
            result += replacements[char]
        else:
            result += f'<sub>{char}</sub>'
    
    return result


def split_text(text):
    '''
    行文字拆分（保留空格）
    '''
    # 将超过四个空格的部分替换为四个空格
    modified_text = re.sub(r'\s{4,}', '    ', text)
    # 使用正则表达式进行分割，保留空格和字符串
    split_list = re.findall(r'\S+|\s{4}', modified_text)
    return split_list


def origin_data_to_record(original_data: dict[str, dict[int, list[list[str]]]]|None) -> dict[str, list[dict[str, str]]]|None:
    """将PDF中获取的原始数据解析为数据记录
    
    :param original_data: 原始数据字典，格式为{子事件名称: {页码: [['IPC(主分类)', 'C22C 47/14'], ['合同备案号', 'X2021140000003']], 页码: [['IPC(主分类)', 'C22C 47/14'], ['合同备案号', 'X2021140000003']],...}}
    :return: 数据记录字典，格式为{子事件名称: [{字段1: 值1, 字段2: 值2,...}, {字段1: 值1, 字段2: 值2,...},...]}
        子事件名称: {
            页码: [['IPC(主分类)', 'C22C 47/14'], ['合同备案号', 'X2021140000003']],
            页码: [['IPC(主分类)', 'C22C 47/14'], ['合同备案号', 'X2021140000003']],
                                }
            }

    """


    def find_redundant_chars(line: list[str]) -> list[str]:
        """
        找出line中的游离字符（没有直接跟在KEYWORD_METADATA中定义的关键词后的字符）
        """
        
        # s = '座便器(RX 陶瓷体)'

        redundant_chars = []
        for i, s in enumerate(line):
            if '----------' in s:
                continue
            elif s == 'VK_PLACEHOLDER_of_patent_name':
                continue
            elif i == 0 and  s not in KEYWORD_METADATA:
                redundant_chars.append(s)
            else:
                if s not in KEYWORD_METADATA and line[i-1] not in KEYWORD_METADATA and line[i-1] != 'VK_PLACEHOLDER_of_patent_name':
                    redundant_chars.append(s)
                else:
                    continue
        
        return redundant_chars


    def is_subscript_line(line: list[str], prev_line: list[str]) -> bool:
        """
        判断该行line是否是下标字符行
        """
        
        def is_chinese(char):
            return re.match(r'[\u4E00-\u9FFF]', char) is not None

        # 判断line中的元素str是否包含空格,如果有，则将该元素按空格拆分，拆分后的元素形成新的列表new_line
        if any(' 'in s for s in line):
            new_line = []
            for s in line:
                if ' ' in s:
                    new_line.extend(s.split(' '))
                else:
                    new_line.append(s)
            line_temp = new_line
        line_temp = line.copy()

        # 判断line中的元素str是否全部为数字或字母，且长度不超过4
        if any(s in prev_line for s in ('发明名称', '实用新型名称', '使用外观设计的产品名称', 'VK_PLACEHOLDER_of_patent_name')):
            if all(s not in KEYWORD_METADATA for s in line_temp):
                # Refactored code to add debug information
                all_conditions_met = True
                for s in line_temp:
                    is_digit = s.isdigit()
                    is_ascii_not_chinese = (not is_chinese(s) and s.isascii())
                    is_infinity = (s == '∞')
                    is_dash = (s == '-')
                    is_non_stoichiometric_sub = (s == '(1-x-y)')
                    condition1 = is_digit or is_ascii_not_chinese or is_infinity or is_dash or is_non_stoichiometric_sub
                    condition2 = len(s) <= 7

                    if TEST_MODE:
                        print(f"--------debug: Checking element '{s}' for subscript line:")
                        print(f"----------debug:   isdigit(): {is_digit}")
                        print(f"----------debug:   (not is_chinese(s) and isascii()): {is_ascii_not_chinese}")
                        print(f"----------debug:   s == '∞': {is_infinity}")
                        print(f"----------debug:   s == '-': {is_dash}")
                        print(f"----------debug:   Condition 1 ({is_digit} or {is_ascii_not_chinese} or {is_infinity} or {is_dash}): {condition1}")
                        print(f"----------debug:   len(s) <= 5 ({len(s)} <= 5): {condition2}")
                        print(f"----------debug:   Overall condition for '{s}': {condition1 and condition2}")

                    if not (condition1 and condition2):
                        if TEST_MODE:
                            print(f"--------debug: Element '{s}' failed the subscript line condition.")
                        all_conditions_met = False
                        break # No need to check further elements in this line

                if all_conditions_met:
                    if TEST_MODE:
                        print(f"--------debug: All elements in {line_temp} passed the subscript line condition.")
                    return True
                else:
                    if TEST_MODE:
                        print(f"--------debug: Not all elements in {line_temp} passed the subscript line condition.")
                    return False
            else:
                return False
        else:
            return False
        

    def process_broken_line(line_num: int, line: list[str], row_data: list[list[str]]) -> tuple[list[str], list[str]|None]:
        """
        处理游离字符行
        """      
        def do_process_redundant_chars(line: list[str], prev_line: list[str]) -> list[str]:
            """
            处理游离字符
            """

            DEBUG_MODE = TEST_MODE
            # DEBUG_MODE = True

            # 定义一个得到line中某个字符最近的关键词('发明名称','实用新型名称','使用外观设计的产品名称')索引的函数
            def get_nearest_in_index(line: list[str], char_index: int) -> int: # type: ignore
                '''
                得到line中某个字符最近的关键词('发明名称','实用新型名称','使用外观设计的产品名称')索引
                '''
                for i in range(char_index, -1, -1):
                    if line[i] in ('发明名称','实用新型名称','使用外观设计的产品名称'):
                        return i
                if i == 0:
                    return -1
            

            # 定义一个判断line中某个字符最近的关键词，是否是('受让人','让与人')的函数
            def is_nearest_in_key(line: list[str], char_index: int) -> bool:
                '''
                判断line中某个字符最近的关键词，是否是('受让人','让与人')其中一个
                '''
                for i in range(char_index, -1, -1):
                    if line[i] in KEYWORD_METADATA:
                        if line[i] in ('受让人','让与人'):
                            return True
                        else:
                            return False
                    else:
                        continue
                return False


            # 定义一个判断传入行（line）中有没有发明名称的函数,有的话返回个数（int）,没有的话返回None
            def has_invention_name(line: list[str]) -> int:
                '''
                判断传入行（line）中有没有发明名称的函数,有的话返回个数（int）,没有的话返回None'''
                count = 0
                for s in line:
                    if s in ('发明名称','实用新型名称','使用外观设计的产品名称'):
                        count += 1
                return count

            
            # 定义一个判断当前字符是否是第一个游离元素的函数
            def is_first_redundant_char(line: list[str], char: str) -> bool:
                '''
                判断当前字符是否是第一个游离元素的函数
                '''
                if re.match(r'^(20)\d{2}\.[0-1][0-9]\.[0-3][0-9]$', line[line.index(char)-1]):  # 日期元素
                    return True
                elif "-------" in line[line.index(char)-1]:   # 分隔符
                    return True
                else:
                    return False


            # 找出line中的游离字符（没有直接跟在KEYWORD_METADATA中定义的关键词后的字符）和游离字符的索引位置
            redundant_chars = find_redundant_chars(line)
            if len(redundant_chars) == 0:
                return line
            
            # index_of_redundant_chars = [line.index(s) for s in redundant_chars]
            # 找出line中的游离字符索引位置
            index_of_redundant_chars = []
            for index, s in enumerate(line):
                if s in redundant_chars:
                    index_of_redundant_chars.append(index)
            print(f"\n----debug: 正在处理第{page_num}页第{line_num}行的游离字符：{redundant_chars} 索引：{index_of_redundant_chars}")  # noqa: E701

            # debug:
            if DEBUG_MODE : print(f"----debug: 原始游离字符行: {line}")  # noqa: E701
            if DEBUG_MODE : print(f"----debug: 游离字符：{redundant_chars} 游离字符索引：{index_of_redundant_chars} ")  # noqa: E701
            
            # 处理游离字符
            new_line = line.copy()
            for ind,char in zip(index_of_redundant_chars, redundant_chars):
                # if DEBUG_MODE :print(f"------debug: 处理游离字符：索引：{ind}  字符：{char} ")
                if ind == 0 :  # 游离字符在行的首:插入占位字符（后续处理时会合并到上一行）
                    new_line.insert(new_line.index(char), 'VK_PLACEHOLDER_of_patent_name')
                elif ind == len(line)-1:   # 游离字符在末尾：空格连接，合并到左侧元素或者插入占位字符
                    last_index_of_invention_name = get_nearest_in_index(line, ind)

                    #debug:
                    # print(f"----debug: 处理last_index_of_invention_name={last_index_of_invention_name}  \nhas_invention_name(line)={has_invention_name(line)}  \nline={line}")

                    if len(line)>=4 and has_invention_name(line)>0 and last_index_of_invention_name>=1:  # 末尾的游离字符:['xx', 'xx', '发明名称', '环状RNA', 'circ-01477及其应用']
                        # print(f"----debug: 处理last_index_of_invention_name={last_index_of_invention_name}  \nhas_invention_name(line)={has_invention_name(line)}  \nline={line}")  
                        # print(f"----debug: new_line={new_line}")

                        if ind == len(line)-1:
                            new_line[-2] += (' ' + char)
                            new_line.pop(-1)
                        elif line.count(char) == 2:
                            def find_second_index(lst, a):
                                indices = [i for i, x in enumerate(lst) if x == a]
                                return indices[1] if len(indices) >= 2 else -1

                            new_line[find_second_index(new_line, char)-1] += (' ' + char)
                            new_line.pop(find_second_index(new_line, char))
                        else:
                            new_line[new_line.index(char)-1] += (' ' + char)
                            new_line.remove(char)
                    elif len(line)<4 and (has_invention_name(line)==1 and has_invention_name(prev_line)==0) \
                            and '使用外观设计的产品名称' in line and last_index_of_invention_name==0:  # 末尾的游离字符:['使用外观设计的产品名称', '座便器(RX', '陶瓷体)']
                        new_line[new_line.index(char)-1] += (' ' + char)
                        new_line.remove(char)
                    elif len(line)>=4 and cal_KWlength_of_line(new_line)==4 and is_nearest_in_key(line, ind):  # 末尾的游离字符:['xx', 'xx', '受让人', 'xx', 'xx']
                        new_line[new_line.index(char)-1] += char
                        new_line.remove(char)
                    else:  # 插入占位字符（后续处理时会合并到上一行）
                        if 'VK_PLACEHOLDER_of_patent_name' in new_line and new_line.index('VK_PLACEHOLDER_of_patent_name') >= 2:
                            break
                        else:
                            new_line.insert(new_line.index(char), 'VK_PLACEHOLDER_of_patent_name')
                else: # 游离字符在中间：空格连接，合并到左侧元素
                    if is_first_redundant_char(line, char): 
                        new_line.insert(new_line.index(char), 'VK_PLACEHOLDER_of_patent_name')
                    elif 'VK_PLACEHOLDER_of_patent_name' in new_line:  #
                        print(f"------debug: 逐一处理游离字符：{char} 发现占位符，中断处理")  # 
                        break
                    else:
                        new_line[new_line.index(char)-1] += (' ' + char)
                        new_line.remove(char)
                    
                # print(f"----debug: 中间的游离字符 {char} 处理后：\n     line = {new_line}")
                
                if DEBUG_MODE:
                    print(f"------debug: 逐一处理游离字符：处理-{char}-后游离字符行: {new_line}")
                                                

            if DEBUG_MODE : print(f"----debug: 修正后的游离字符行: {i} = {new_line}\n      上一行：{prev_line}\n")  # noqa: E701
            # 调整长度
            # list_of_breaking_line = [s for s in new_line if '---------' in s]
            # assert cal_KWlength_of_line(new_line) == 4, f"----debug: 修正后第{page_num}页当前行{line_num}等效长度：{cal_KWlength_of_line(new_line)} 不是4:{i} = {new_line}"
            # assert (len(new_line) + len(list_of_breaking_line))%2 == 0 , f"----debug: 修正后当前行长度：{len(new_line) + len(list_of_breaking_line)} 为奇数:{i} = {new_line}"

            return new_line


        def do_process_subscript_chars(line: list[str], prev_line: list[str]) -> tuple[list[str], list[str]]:
            """
            处理下标字符
            """
            
            def find_broken_fragments(prev_line: list[str]) -> list[str]:
                '''
                获取prev_line中发明名称字段的异常值(存在空格间隙的文字)，用于和下标字符行的游离字符进行合并
                '''

                # debug：
                #  ['发明名称', '一种单分散磁性能可控Fe', 'O', '-SiO', '核壳球簇的制备', '发明名称', '一种智能型高压互感器']
                # print(f'----debug: 传入的prev_line={prev_line}')

                str_of_broken_fragments = []
                
                # 算法1：
                # switch = False
                # for s in prev_line.copy():
                #     if s in ('发明名称', '实用新型名称', '使用外观设计的产品名称', 'VK_PLACEHOLDER_of_patent_name'):
                #         if switch and len(str_of_broken_fragments) == 1:    # 行内正常的发明名称  ['发明名称', 'xxx', '发明名称', 'xx', 'xx', 'xx']
                #             str_of_broken_fragments.clear()
                #         elif switch:
                #             str_of_broken_fragments.append(s)
                #             # continue
                #         switch = True
                #     elif s in KEYWORD_METADATA:
                #         switch = False
                #     else:
                #         if switch:
                #             str_of_broken_fragments.append(s)
                #         else:
                #             continue

                # 算法2：
                ind_kw = None   # 默认关键字位置
                for i,s in enumerate(prev_line.copy()):
                    if s in KEYWORD_METADATA+('VK_PLACEHOLDER_of_patent_name',):
                        ind_kw = i
                    else:
                        if i == 0:   # 第一个元素即为破碎字符
                            ind_kw = -1
                        elif ind_kw is not None and i - ind_kw == 1:
                            continue
                        elif ind_kw is not None and i - ind_kw == 2:
                            str_of_broken_fragments.append(prev_line[i-1])
                            str_of_broken_fragments.append(s)
                        elif ind_kw is not None and i - ind_kw > 2:
                            str_of_broken_fragments.append(s)
                        else:
                            raise ValueError(f"----debug: 异常情况：\n   原行：{prev_line}  \n   异常字符：{s}  \n   异常索引：{i}")
                
                # 处理特殊情形下关键字被列入异常值
                # if str_of_broken_fragments[-1] in KEYWORD_FORMAT:
                #     str_of_broken_fragments.pop()  # 去掉末尾的'发明名称'


                return str_of_broken_fragments  # 取与下标字符长度匹配的前几个字符，以便与下标字符合并
            
            
            def fix_special_cases(line: list[str]) -> list[str]:
                """
                处理一些特殊情况
                """
                special_chracters = {
                                    'SbZnO' : ('Sb', ' ', 'Zn', ' ', 'O', ' '),
                                    'SrCe': ('Sr', ' ', 'Ce', ' '),
                                    'FeO' : ('Fe', ' ', 'O', ' '),
                                    'Co@InO' : ('Co@In', ' ', 'O', ' '),
                                    'AlO' : ('Al', ' ', 'O', ' '),

                                    'CuO' : ('Cu', ' ', 'O'),
                                    'MNbO' : ('MNb', ' ', 'O'),
                                    'VO' : ('V', ' ', 'O'),
                                    'LiFeF' : ('LiFe', ' ', 'F'),
                                    'CoO' : ('Co', ' ', 'O'),
                                    'TiN-InS' : ('TiN-In', ' ', 'S'),
                                    '6-氯-3-(苯基-d)-茚-1-酮' : ('6-氯-3-(苯基-d', ' ', ')-茚-1-酮'),
                                    '基-d)-2,3-二氢-1H-茚-1-基)-2,2-二甲基-1-(甲' : ('基-d', ' ', ')-2,3-二氢-1H-茚-1-基)-2,2-二甲基-1-(甲'),
                                    '基-d)哌嗪' : ('基-d', ' ', ')哌嗪'),
                                    'g-CN' : ('g-C', ' ', 'N', ' '),

                                    'MoS' : ('MoS', ' '),
                                    'NiCo/TiO' : ('NiCo/TiO', ' '),
                                    'CO' : ('CO', ' '),

                                    '(MoO)': ('(MoO', ' ', ')'),
                                    'Ln)' : ('Ln', ' ', ')'),
                                    '(PO)' : ('(PO', ' ', ')'),
                                    '(SO)' : ('(SO', ' ', ')'),
                                    '（TiHfX）（NiCu）' : ('（TiHfX）', ' ', '（NiCu）'),
                                    '汽车后门内饰板（A301）' :('汽车后门内饰板（A301', ' ', '）'),
                                    '汽车前门内饰板（A301）' :('汽车前门内饰板（A301', ' ', '）'),

                                    # 待验证普适应的
                                    'ABO' : ('AB', ' ', 'O', ' '),
                                    '电流密度Bi' : ('电流密度Bi', ' '),

                                    # 优先级靠后的、有待验证的
                                    'TiO' : ('Ti', ' ', 'O', ' '),    # 波及范围太广、没有通用性，待进一步优化
                                    
                                        }
                #例外字符组合
                white_combinations = {
                                    'TiO' : ('MoTiO',),
                                    'VO' : ('BiVO', 'Bi VO', 'FeVO', 'Bi₇VO', '片状Bi'),
                                    'FeO' : ('BiFeO', )
                                    
                                    }
                
                
                new_line = []
                for el in line:
                    # for dev :
                    # if "一种微波水热法制备片状Bi" in el:
                    #     raise ValueError(f"----debug: 特殊情况：\n   原行：{line}  \n   异常字符：{el}  \n   异常索引：{line.index(el)}")

                    # print(f"----debug: 处理元素= --{el}--")
                    if any(s in el for s in special_chracters):    # line中当前元素el存在特殊字符
                        for special_chracter,fix_SC_tuple in special_chracters.items():       # 遍历特殊字符字典
                            if special_chracter in el:
                                if special_chracter in white_combinations and any(s in el for s in white_combinations[special_chracter] for el in line):
                                    continue
                                else:
                                    el = el.replace(special_chracter, ''.join(fix_SC_tuple))
                                # print(f"----debug:  修正后el= -{el}-")
                                
                                # for dev :
                                # if '片状Bi' in el:
                                #     raise ValueError(f"----debug: 特殊情况：\n   原行：{line}  \n   异常字符：{el}  \n   异常索引：{line.index(el)}")
                            else:
                                continue
                            
                        if len(new_line)>0 and new_line[-1]=='':
                            new_line.pop()
                        new_line.extend(el.split(' '))
                        # print(f"----debug:  修正后new_line_T=：{new_line}")
                    else:
                        if len(new_line)>0 and new_line[-1]=='' and el not in KEYWORD_METADATA+('VK_PLACEHOLDER_of_patent_name',):
                            new_line.pop()
                        new_line.append(el)
                        # print(f"----debug: 无修正new_line_T= {new_line}")
                    
                return new_line
            
            
            # DEBUG_MODE = True
            # TEST_MODE = False
            DEBUG_MODE = TEST_MODE
            # 下标字符行预处理: 当前行line元素字符串中有空格的话，拆分成多个元素
            if any(' 'in s for s in line):
                new_line = []
                for s in line:
                    if ' ' in s:
                        new_line.extend(s.split(' '))
                    else:
                        new_line.append(s)
                line = new_line
            assert is_subscript_line(line, prev_line), f'----debug: 非法下标字符行：{line}'  #  ['3', '4'] 两个下标字符错行解析


            str_of_broken_fragments = find_broken_fragments(prev_line)          # 找出上一行的间隙字符列表
            if DEBUG_MODE : print(f"--------debug: 特殊情形处理前：\n--str_of_broken_fragments= {str_of_broken_fragments},\n--prev_line= {prev_line}")  # noqa: E701

            # 特殊行预处理A情形：空隙比下标字符多
            if prev_line == ['申请公布日', '2016.05.11', '授权公告日', '2018.08.31', '发明名称', '一种检测血清抗Annexin', 'A', '-IgG抗体的试剂盒']:
                prev_line = ['申请公布日', '2016.05.11', '授权公告日', '2018.08.31', '发明名称', '一种检测血清抗Annexin A', '-IgG抗体的试剂盒']
            else:
                pass  
            
            # 特殊行预处理函数：修正特殊化学式
            prev_line = fix_special_cases(prev_line)  # 特殊情况处理
            str_of_broken_fragments = find_broken_fragments(prev_line) 
            if DEBUG_MODE : print(f"--------debug: fix_special_cases后：\n--str_of_broken_fragments= {str_of_broken_fragments},\n--prev_line= {prev_line}")  # noqa: E701

            # 特殊行预处理B情形：空隙比下标字符少
            if prev_line == ['申请公布日', '2021.06.29', '授权公告日', '2021.08.10', '发明名称', 'BC/Al复合材料及其制备方法']:
                prev_line = ['申请公布日', '2021.06.29', '授权公告日', '2021.08.10', '发明名称', 'B', 'C/Al复合材料及其制备方法']
            elif prev_line == ['法', '发明名称', '连续铸轧制备BC/Al中子吸收材料板材的方法']:
                prev_line = ['法', '发明名称', '连续铸轧制备B', 'C/Al中子吸收材料板材的方法']
            elif prev_line == ['---------------------------------------', '发明名称', '一种Sm(MoO', ')', '薄膜的直接制备方法']:
                prev_line = ['---------------------------------------', '发明名称', '一种Sm', '(MoO', ')', '薄膜的直接制备方法']
            elif prev_line == ['发明名称', '一种基于[CuICl]多吡啶基配合物的制备方法', '受让人', '河北一然生物科技股份有限公司']:
                prev_line = ['发明名称', '一种基于[Cu','<sup>I</sup>Cl', ']多吡啶基配合物的制备方法', '受让人', '河北一然生物科技股份有限公司']
            elif prev_line == ['受让人', '江苏开创检测技术有限公司', '发明名称', '一种二维VCr', 'CS', '纳米片的制备方法']:
                prev_line = ['受让人', '江苏开创检测技术有限公司', '发明名称', '一种二维V', 'Cr', 'CS', '纳米片的制备方法']                    
            elif prev_line == ['申请公布日', '2022.04.08', '授权公告日', '2023.03.31', '发明名称', 'CuZn', 'Mg', 'SnS', '纳米晶的制备方法及其用途']:
                prev_line = ['申请公布日', '2022.04.08', '授权公告日', '2023.03.31', '发明名称', 'Cu', 'Zn', 'Mg', 'SnS', '纳米晶的制备方法及其用途']
            elif prev_line == ['发明名称', '磁性ZnFeO', '/埃洛石复合吸附材料及其制备方法', '让与人', '常州大学']:
                prev_line = ['发明名称', '磁性ZnFe', 'O', '/埃洛石复合吸附材料及其制备方法', '让与人', '常州大学']
            elif prev_line == ['发明名称', '一种g-CN', '/CDs/β-FeOOH光催化材料及其制备方法', '发明名称', '一种胀管机上管端夹紧装置及其夹紧方法']:
                prev_line = ['发明名称', '一种g-C', 'N', '/CDs/β-FeOOH光催化材料及其制备方法', '发明名称', '一种胀管机上管端夹紧装置及其夹紧方法']
            elif prev_line == ['发明名称', '可见光响应的光催化剂Bi', 'SbZnO', '发明名称', '一种一步制备贵金属/SiO', '纳米复合粒子的方法']:
                prev_line = ['发明名称', '可见光响应的光催化剂Bi', 'Sb', 'Zn', 'O', '发明名称', '一种一步制备贵金属/SiO', '纳米复合粒子的方法']
            elif prev_line == ['发明名称', '可见光响应的光催化剂SrCe', 'Ti', 'O', '及其制备方法', '其制备方法和应用']:
                prev_line = ['发明名称', '可见光响应的光催化剂Sr', 'Ce', 'Ti', 'O', '及其制备方法', '其制备方法和应用']
            elif prev_line == ['IPC(主分类)', 'C08G 69/42', '合同备案号', 'X2023980046406', '发明名称', '一种化学式为[C', 'H', 'NNd', 'O', ']', '金属有机框架化合物']:
                prev_line = ['IPC(主分类)', 'C08G 69/42', '合同备案号', 'X2023980046406', '发明名称', '一种化学式为[C', 'H', 'N', 'Nd', 'O', ']', '金属有机框架化合物']
            elif prev_line == ['受让人', '泸溪县鑫兴冶化有限公司', '发明名称', '可见光响应的光催化剂BiSb', 'Zn', 'O']:
                prev_line = ['受让人', '泸溪县鑫兴冶化有限公司', '发明名称', '可见光响应的光催化剂Bi', 'Sb', 'Zn', 'O', '']
            elif prev_line == ['受让人', '杭州鑫龙华知商标代理有限公司', '发明名称', '一种CN', '纳米线的制备方法']:
                prev_line = ['受让人', '杭州鑫龙华知商标代理有限公司', '发明名称', '一种C', 'N', '纳米线的制备方法']
            elif prev_line == ['发明名称', '一种链珠状Cu', 'O-MnO', '/NiO复合材料及其制备方法', '发明名称', '硫化铋镍正极材料的制备方法及其应用']:
                prev_line = ['发明名称', '一种链珠状Cu', 'O-Mn', 'O', '/NiO复合材料及其制备方法', '发明名称', '硫化铋镍正极材料的制备方法及其应用']
            elif prev_line == ['发明名称', 'P掺杂FeS/Co', 'S/Co', 'S', '复合材料的制备方法及其应用', '申请公布日', '2016.10.12', '授权公告日', '2018.03.06']:
                prev_line = ['发明名称', 'P掺杂FeS/Co', 'S', '/Co', 'S', '复合材料的制备方法及其应用', '申请公布日', '2016.10.12', '授权公告日', '2018.03.06']
            elif prev_line == ['发明名称', '氨基功能化多孔CO吸附材料的制备方法', '发明名称', '一种减少齿轮微点蚀的齿轮修形方法']:
                prev_line = ['发明名称', '氨基功能化多孔CO', '吸附材料的制备方法', '发明名称', '一种减少齿轮微点蚀的齿轮修形方法']
            elif prev_line == ['申请公布日', '2021.06.01', '授权公告日', '2022.05.17 Al O', 'VK_PLACEHOLDER_of_patent_name', '/W合金粉体']:
                prev_line = ['申请公布日', '2021.06.01', '授权公告日', '2022.05.17', 'VK_PLACEHOLDER_of_patent_name',  'Al', 'O', '/W合金粉体']
            elif prev_line == ['发明名称', '基于复合膜系统的脱硫废水回收再利用工艺', '发明名称', 'Co@InO', '/C复合光催化剂及其制备方法和应用']:
                prev_line = ['发明名称', '基于复合膜系统的脱硫废水回收再利用工艺', '发明名称', 'Co@In', 'O', '/C复合光催化剂及其制备方法和应用']
            elif prev_line == ['发明名称', '利用复合溶剂回收粘胶纤维废气中CS的方法', '受让人', '新沂市宝庐缘建材加工厂']:
                prev_line = ['发明名称', '利用复合溶剂回收粘胶纤维废气中CS', '的方法', '受让人', '新沂市宝庐缘建材加工厂']
            elif prev_line ==  ['发明名称', '利用复合溶剂回收粘胶纤维废气中CS的方法', '受让人', '四川英鉴信息科技有限公司']:
                prev_line =  ['发明名称', '利用复合溶剂回收粘胶纤维废气中CS', '的方法', '受让人', '四川英鉴信息科技有限公司']
            elif prev_line == ['许可种类', '普通许可', '备案日期', '2023.10.30', '发明名称', '可见光响应的光催化剂SrCe', 'Ti', 'O', '及其制备方法']:
                prev_line = ['许可种类', '普通许可', '备案日期', '2023.10.30', '发明名称', '可见光响应的光催化剂Sr', 'Ce', 'Ti', 'O', '及其制备方法']
            elif prev_line == ['申请公布日', '2022.08.09', '授权公告日', '2023.08.11', '发明名称', '可见光响应的光催化剂Bi', 'SbZnO']:
                prev_line = ['申请公布日', '2022.08.09', '授权公告日', '2023.08.11', '发明名称', '可见光响应的光催化剂Bi', 'Sb','Zn', 'O', '']
            elif prev_line == ['许可种类', '普通许可', '备案日期', '2024.03.26', '发明名称', '高Cr含量CrB-Cr涂层的制备工艺']:
                prev_line = ['许可种类', '普通许可', '备案日期', '2024.03.26', '发明名称', '高Cr含量CrB', '-Cr涂层的制备工艺']
            elif prev_line == ['发明名称', '一种CsPb', 'TiI', '红光微晶玻璃及其制备方法', '让与人', '四川制药制剂有限公司']:
                prev_line = ['发明名称', '一种CsPb', 'Ti', 'I', '红光微晶玻璃及其制备方法', '让与人', '四川制药制剂有限公司']
            elif prev_line == ['受让人', '温岭市中发精密钢件有限公司', '发明名称', '可见光响应的光催化剂SrCe', 'Ti', 'O', '及其制备方法']:
                prev_line = ['受让人', '温岭市中发精密钢件有限公司', '发明名称', '可见光响应的光催化剂Sr', 'Ce', 'Ti', 'O', '及其制备方法']
            elif prev_line == ['受让人', '台州市石夫人鞋业有限公司', '发明名称', '可见光响应的光催化剂Bi', 'SbZnO']:    
                prev_line = ['受让人', '台州市石夫人鞋业有限公司', '发明名称', '可见光响应的光催化剂Bi', 'Sb', 'Zn', 'O', '']
            elif prev_line == ['发明名称', 'Ni、S共掺杂Ti', 'O', '2薄膜及其应用和制备方法', '发明名称', '一种Pr', '(MoO', ')', '薄膜的直接制备方法']:
                prev_line = ['发明名称', 'Ni、S共掺杂TiO2薄膜及其应用和制备方法', '发明名称', '一种Pr', '(MoO', ')', '薄膜的直接制备方法']
            elif prev_line == ['受让人', '东台高鑫机械装备有限公司', '发明名称', '可见光响应的光催化剂Ba', 'MoTi', 'O', '及其制备方法']:
                prev_line = ['受让人', '东台高鑫机械装备有限公司', '发明名称', '可见光响应的光催化剂Ba', 'MoTiO', '及其制备方法']
            elif prev_line ==  ['发明名称', '一种中空Ti', 'O', '/MoS', '复合材料及其制备方法', '让与人', '南京大学']:
                prev_line =  ['发明名称', '一种中空TiO', '/MoS', '复合材料及其制备方法', '让与人', '南京大学']
            elif prev_line == ['许可种类', '普通许可', '备案日期', '2022.12.01', '发明名称', '一种中空Ti', 'O', '/MoS', '复合材料及其制备方法']:
                prev_line = ['许可种类', '普通许可', '备案日期', '2022.12.01', '发明名称', '一种中空TiO', '/MoS', '复合材料及其制备方法']
            elif prev_line == ['发明名称', '一种中空Ti', 'O', '/MoS', '复合材料及其制备方法', '受让人', '沈阳大陆激光成套设备有限公司']:
                prev_line = ['发明名称', '一种中空TiO', '/MoS', '复合材料及其制备方法', '受让人', '沈阳大陆激光成套设备有限公司']
            else:
                pass
            
            str_of_broken_fragments = find_broken_fragments(prev_line)  
            if DEBUG_MODE : print(f"--------debug: 特殊情形处理后：\n--str_of_broken_fragments= {str_of_broken_fragments}\n--prev_line= {prev_line}\n--line= {line}")  # noqa: E701

            # 校验
            # assert len(str_of_broken_fragments) >= len(line)+1, f"----debug: 未预期(间隙小于下标字符数量){page_num}页本行（{line}）上一行（{prev_line}）的发明名称字段的破碎字符:{str_of_broken_fragments}"
            
            
            # 合并下标字符
            # 合并间隙字符和下标字符，生成临时列表
            if DEBUG_MODE : print(f"----debug: 合并下标字符前：line({page_num}:{line_num}) = {line}\n      prev_line({page_num}:{line_num-1}) = {prev_line}\n      str_of_broken_fragments= {str_of_broken_fragments}")  # noqa: E701
            # 算法2：直接合并prev_line和line(下标字符所在的行)
            sc_line = line.copy()
            ind_kw = None
            temp_line = []
            for i,el in enumerate(prev_line):
                if el in KEYWORD_METADATA+('VK_PLACEHOLDER_of_patent_name',) or '------' in el:
                    ind_kw = i
                    temp_line.append(el)
                else:
                    if i == 0:
                        ind_kw = -1
                        temp_line.append(el)
                    elif ind_kw is not None and i - ind_kw == 1:
                        temp_line.append(el)
                    elif ind_kw is not None and i - ind_kw > 1:
                        if sc_line != [] :
                            sc = sc_line.pop(0)
                            temp_line.append(subscript_char(sc))
                            temp_line.append(el)
                        else:
                            temp_line.append(el)
                    else:
                        raise ValueError(f"----debug: 错误的索引位置：{i}，{el}")
                    
            if len(sc_line) == 1:  # 只剩下一个下标字符时，插入到最后一个破碎字符的后面
                sc = sc_line.pop()
                last_str_broken_fragments = str_of_broken_fragments[-1]
                temp_line.insert(temp_line.index(last_str_broken_fragments)+1, subscript_char(sc))

            assert len(sc_line)==0, f"----debug: 剩余下标字符未合并：{sc_line},prev_line:{temp_line}"
            prev_line = temp_line
  
            if DEBUG_MODE : print(f"----debug: 处理（合并下标字符）后上一行：{prev_line}")  # noqa: E701
 
            # 行内合并
            new_line = []
            count = 2  # 计数器，用于合并行内字符(下标+间隙字符)
            for el in prev_line:
                if el in KEYWORD_METADATA+('VK_PLACEHOLDER_of_patent_name',) or '------' in el:
                    new_line.append(el)
                else:
                    if len(new_line) == 0:
                        new_line.append(el)
                    else:
                        if new_line[-1] in KEYWORD_METADATA or new_line[-1] == 'VK_PLACEHOLDER_of_patent_name':
                            new_line.append(el)
                        else:
                            if count <= len(line)*2 + 1:
                                # print(f"----debug:count={count}, 合并前：{new_line[-1]} + 字符：{el}")
                                new_line[-1] += el
                            else:
                                new_line.append(el)
                            
                            count += 1
            line = []
            prev_line = new_line
            if DEBUG_MODE : print(f"----debug: 处理（合并行内字符）后上一行：{prev_line}")  # noqa: E701

            # 校验并处理修复后的上一行，是否还有游离字符
            if find_redundant_chars(prev_line):
                if DEBUG_MODE : print(f"----debug: 处理下标字符后，发现游离字符：{find_redundant_chars(prev_line)}")  # noqa: E701
                prev_line = do_process_redundant_chars(prev_line, row_data[line_num-2])
            if DEBUG_MODE : print(f"----debug: 下标函数处理后：line({page_num}:{line_num}) = {line}\n      prev_line({page_num}:{line_num-1}) = {prev_line}\n")  # noqa: E701
            
            # assert cal_KWlength_of_line(prev_line) == 4, f"----debug: 下标函数修正后上一行长度：{cal_KWlength_of_line(prev_line)} 不是4:{prev_line}"
            # debug:
            if any('一种中空Ti₂O₂/MoS' in s for s in prev_line):  # 一种中空Ti₂O₂/MoS 复合材料及其制备方法
                raise ValueError(f"----debug: 发现目标调试数据：{prev_line}")

            return line, prev_line  


        try:
            next_line = row_data[line_num+1]
        except IndexError: # 页面的最后一行
            # print(f"----debug: 本行为最后一行：{line_num} = {line}，上一行为：{row_data[line_num-1]}")
            if is_subscript_line(line, row_data[line_num-1]):  # 最后一行是下标字符行
                line, prev_line = do_process_subscript_chars(line, row_data[line_num-1])
                return line, prev_line
            else:
                line = do_process_redundant_chars(line, row_data[line_num-1])
                return line, None
        else:  # 中间行
            if len(find_redundant_chars(line)) == 0:  # 正常行，不处理
                # print(f"----debug: 无游离字符行：{line}")
                return line, None
            else:
                if is_subscript_line(next_line, line):  #  当是下标字符行的上一行时，不做处理，留待后续逻辑（处理下标字符的代码）处理
                    print(f"----debug: 发现下标字符上一行：{line} ，下一行：{next_line}")
                    return line,None
                elif is_subscript_line(line, row_data[line_num-1]):    #  处理下标字符行
                    print(f"\n----debug: 发现下标字符行{line_num}：{line}, \n------其上一行为：{row_data[line_num-1]} \n------其下一行为：{row_data[line_num+1]}")
                    line, prev_line = do_process_subscript_chars(line, row_data[line_num-1])
                    # print(f"----debug: 修正后的下标字符行：{line}，上一行：{prev_line}")
                    assert cal_KWlength_of_line(prev_line) == 4, f"----debug: 修正后的第{page_num}页第{line_num-1}行下标字符行长度不为4，请检查数据：{prev_line}"
                    return line,prev_line
                else:   # 处理游离字符行
                    line = do_process_redundant_chars(line, row_data[line_num-1])

                    assert cal_KWlength_of_line(line) == 4, f"----debug: 修正后的第{page_num}页第{line_num}行游离字符行长度为{cal_KWlength_of_line(line)}，请检查数据：{line}"
                    return line, None
        

    def find_key_with_missing_value(line: list[str]) -> dict[int, str]:
        """寻找行字符串列表中的缺失值

        
        Args:
            line (list[str]): 行数据
        Returns:
            dict[int, str]: 缺失值的关键字及其下标
        """

        key_with_missing_value = {}      # 缺失值的关键字及其下标
        for i, s in enumerate(line):
            if s in KEYWORD_METADATA:     
                if line[i+1] in KEYWORD_METADATA:     
                    key_with_missing_value[i] = s
                else:
                    continue           
        
        return key_with_missing_value
    

    def split_line_to_left_and_right(sub_event: str, i:int, line: list[str]) -> tuple[list[str], list[str]]:
        """
        按业务分为左右两段
        """
        row_data_left = []
        row_data_right = []
        if '生效' in sub_event:
            if '------' in ''.join(line[0]):
                row_data_left.append([line[0]])
                row_data_right.append(line[1:])
            elif '------' in ''.join(line[-1]):
                row_data_left.append(line[:-1])
                row_data_right.append([line[-1]])
            else:
                # 页面切分
                if len(line) % 2 == 0:
                    # print(f"----debug: 处理前：{len(line)}={line}")
                    if len(line) == 8:
                        row_data_left.append(line[:len(line)//2])
                        row_data_right.append(line[len(line)//2:])
                    elif len(line) == 6:
                        if line[0] in ('发明名称', '实用新型名称', '使用外观设计的产品名称', '让与人', '受让人', 'VK_PLACEHOLDER_of_patent_name', '备案日期'):
                            row_data_left.append(line[:2])
                            row_data_right.append(line[2:]) 
                        elif line[0] in ('IPC(主分类)', '主分类号', '申请公布日', '授权公告日',  '专利申请号', '专利号', '许可种类'):
                            row_data_left.append(line[:4])
                            row_data_right.append(line[4:]) 
                        else:
                            raise ValueError(f"----debug: 未定义行:{i} = {line}")
                    elif len(line) == 4:
                        if line[0] in ('IPC(主分类)', '主分类号', '申请公布日', '授权公告日',  '专利申请号', '专利号', '许可种类'):
                            row_data_left.append(line)
                        else:
                            row_data_left.append(line[:2])
                            row_data_right.append(line[2:]) 
                    elif len(line) == 2:   
                        if line[0] in ('发明名称', '实用新型名称', '使用外观设计的产品名称', '让与人', '受让人', 'VK_PLACEHOLDER_of_patent_name', '备案日期'):
                            row_data_left.append(line)
                        else: # 长度为2的元素行,但均为上一行的数据延伸行
                            assert all(kw not in line for kw in KEYWORD_METADATA), f"----debug: 未预期行:{i} = {line}"
                            if any(re.match(r'[\u4e00-\u9fa5]+', s) for s in line):  # 包含中文
                                row_data_left.append(line[ : 1])
                                row_data_right.append(line[1 : ])
                            else:  #  发明名称  一种N/Co3多孔复合材料的制备方法 : ['3', '4']
                                raise ValueError(f"----debug: 未预期元素行:{i} = {line}")
                    else:
                        raise ValueError(f"----debug: 未预期长度元素行:{i} = {line}")
                else:
                    if find_key_with_missing_value(line):  # 发现缺失值行
                        print(f"----debug: 发现缺失值行：{i} = {line} 缺失值：{find_key_with_missing_value(line)}")
                        key_with_missing_value = find_key_with_missing_value(line)
                        for index, key in key_with_missing_value.items():
                            line.insert(index+1, '')
                        return split_line_to_left_and_right(sub_event, i, line)
                    else:
                        raise ValueError(f"----debug:第{page_num}页 奇数元素行:{i} = {line}, 上一行：{row_data[i-1]}")
        elif '变更' in sub_event or '注销' in sub_event:
            row_data_left.append(line)
        else:
            raise ValueError(f'未识别的子事件：{original_data.keys()}') # type: ignore
        
        if '生效' in sub_event:
            assert len(row_data_left) == 1 or len(row_data_right) == 1, f"----debug: {i}行={line}\n左右切分异常：{row_data_left}, {row_data_right}"
        return row_data_left, row_data_right


    def cal_KWlength_of_line(line: list[str]) -> int:
        '''
        根据数据行中对应的关键词，计算数据行的等效长度
        '''

        A_KEY_LIST = ('IPC(主分类)', '主分类号', '合同备案号', '专利申请号', '专利号', '申请日','申请公布日', '授权公告日', '许可种类')
        B_KEY_LIST = ('让与人', '受让人', '发明名称', '实用新型名称', '使用外观设计的产品名称', 'VK_PLACEHOLDER_of_patent_name')
        C_KEY_LIST = ('备案日期',) 
        
        KWlength = 0
        try:
            for i,s in enumerate(line):
                if s in A_KEY_LIST:
                    KWlength += 1
                elif s in B_KEY_LIST or "------" in s:
                    KWlength += 2
                elif s in C_KEY_LIST:
                    if i == 0 or (KWlength==2 and i==len(line)-2):
                        KWlength += 2
                    else:
                        KWlength += 1
                else:
                    continue
        except Exception as e:
            raise ValueError(f"----debug: 计算数据行长度异常：{e}，行数据：{line}")
        
        return KWlength
    

    # 定义一个根据KEY_FORMAT计算line[0]关键字行值差的函数
    def cal_line0_diff(current_line_num: int, rowdata: list[str]) -> int:
        '''
        计算line[0]关键字行值差
        '''

        assert len(rowdata) > 0, f"----debug: 输入数据为空{rowdata}"

        prev_line_num = current_line_num - 2
        while True:
            # print(f"----debug: 计算line[0]关键字行值差：当前行{current_line_num}，上一行{prev_line_num}")
            if rowdata[prev_line_num] != []:
                break
            else:
                prev_line_num -= 1

        if '------' in rowdata[prev_line_num][0]:
            return KEYWORD_FORMAT[rowdata[current_line_num][0]] - 0
        else:
            return KEYWORD_FORMAT[rowdata[current_line_num][0]] - KEYWORD_FORMAT[rowdata[prev_line_num][0]]

    

    # 预处理：检测原始数据是否为空
    if original_data is None:
        return None
    else:
        # print(f"----debug: 数据解析函数 输入数据：{original_data.keys()}")
        pass
    
    # 一、整理原始数据 -> 清理、修正、切分原始页面，形成按子事件的数据行
    origin_record:dict[str, list[dict[str, str]]] = {}
    for sub_event, pages_data in original_data.items():
        # print(f"----debug: 按子事件处理：{sub_event}  页数：{len(pages_data)}")
        # break
        
        # 按页码逐页处理（分类号、下标、左右切分）
        origin_record[sub_event] = []
        for page_num, row_data in pages_data.items():
            # --debug: 查看处理前的数据
            # if page_num == 476:
            #     print(f"\n----debug: 1-4 处理前的第{page_num}页数据：")
            #     [print(row) for row in row_data]
            #     break

            # 1、分类号、专利号解析修正
            for i, line in enumerate(row_data): 
                # print(f'line = {line}')
                for j, s in enumerate(line):
                    # debug: 打印出错行
                    # print(f's = {s}, j = {j}')
                    if any(kw in line for kw in ('IPC(主分类)', '主分类号')):   # 如果分类关键字，在本行内出现
                        if re.match(r'^[A-Za-z0-9]{4,5}$', s) and re.match(r'^\d+/\d+$', line[j+1]): # 分类号元素合并
                            row_data[i][j] = ' '.join(line[j:j+2])
                            row_data[i][j+1] = ''
                    if s == 'ZL' and line[j-1] == '专利号' and line[j+1] and re.match(r'^(20)\d{8,10}\.[0-9X]{1}$', line[j+1]): # 专利号元素合并
                        row_data[i][j] = ' '.join(line[j:j+2])
                        row_data[i][j+1] = ''
                row_data[i] = [cell for cell in row_data[i] if cell != '']  # 去掉空元素

            # 2、处理下标字符和游离字符
            for i, line in enumerate(row_data):
                if '生效' in sub_event:
                    line,prev_line = process_broken_line(i, line, row_data)
                    row_data[i] = line
                    if prev_line:
                        # print(f"----debug: 上一行：{row_data[i-1]}")
                        row_data[i-1] = prev_line
                    continue
                elif '变更' in sub_event or '注销' in sub_event:
                    # print(f"----debug: 变更或注销 事件行 ：{list(original_data.keys())[-1]}")
                    break   
                else:
                    raise ValueError(f'未识别的子事件：{original_data.keys()}')
            
            row_data = [row for row in row_data if row != []]        # 去掉空行
            # --debug: 查看处理后的数据
            # if page_num == 684:
            #     print(f"\n----debug: 1-2步处理后的第{page_num}页数据：")
            #     [print(row) for row in row_data]
            #     break
            
            # 3、因生僻字小图片导致的错行修复
            for i, line in enumerate(row_data):
                if cal_KWlength_of_line(line)==2 and cal_KWlength_of_line(row_data[i-1])==2 \
                                                and len(find_redundant_chars(line))==0 :    # 生僻字小图片导致的错行
                    # print(f"\n----debug: 第{page_num}页发现生僻字小图片导致的错行： \n------{i-3}：{row_data[i-3]} \n------{i-2}：{row_data[i-2]} \n------{i-1}：{row_data[i-1]} \n------当前行{i}为: {line} \n------{i+1}：{row_data[i+1]}")
                    
                    if '------' in line[0]:
                        if row_data[i - 2][0] in ('许可种类', '备案日期') and '------' in line[0]:
                            row_data[i - 1] = line + row_data[i - 1] 
                        else:
                            row_data[i - 1] = row_data[i - 1] + line
                    else:
                        if cal_line0_diff(i, row_data[i]) == 1:
                            row_data[i - 1] = line + row_data[i - 1] 
                        else:
                            row_data[i - 1] = row_data[i - 1] + line
                    
                    row_data[i] = []
                    # print(f"----debug: 修正后：\n------{i-3}：{row_data[i-3]} \n------{i-2}：{row_data[i-2]} \n------{i-1}：{row_data[i-1]} \n------当前行{i}为: {row_data[i]} \n------{i+1}：{row_data[i+1]}")
                else:
                    continue
            
            row_data = [row for row in row_data if row != []]        # 去掉空行

            # 1-3 处理后校验 “专利许可合同备案” 子事件数据行，每行关键字等效长度都为4
            for i, line in enumerate(row_data):
                # print(f'----debug: 第{page_num}页 第{i}/{len(row_data)}行 关键字等效长度为{cal_KWlength_of_line(line)}')
                if '专利实施许可合同备案的生效' in sub_event:
                    if i == len(row_data) - 1  and page_num == list(pages_data.keys())[-1]:
                        continue
                    else:
                        # assert cal_KWlength_of_line(line) == 4, f"----debug: 第{page_num}页 第{i}行 关键字等效长度为{cal_KWlength_of_line(line)}，数据行：{line}"
                        pass
                else:
                    break
            # --debug: 查看处理后的数据
            # if page_num == 684:
            #     print(f"\n----debug: 1-3步处理后的第{page_num}页数据：")
            #     [print(row) for row in row_data]
            #     break


            # 4、页面左右切分拼接
            row_data_left = []
            row_data_right = []
            for i, line in enumerate(row_data):
                line_left, line_right = split_line_to_left_and_right(sub_event, i, line)
                row_data_left.extend(line_left)
                row_data_right.extend(line_right)
            current_page_row_data = row_data_left + row_data_right
            
            # --debug: 查看处理后的数据
            if page_num == 288:
                print(f"----debug: 左右切分后的第 {page_num} 页数据：")
                [print(row) for row in current_page_row_data]

            origin_record[sub_event] += current_page_row_data
    
    # debug:
    # print(f"\n---- 获取的PDF解析后的按行数据记录 : {origin_record.keys()}")
    # {print(f"----{k} = {len(v)}") for k,v in origin_record.items()}
    # switch = None
    # window_len = 20
    # for k,v in origin_record.items():
    #     print(f"\n----{k} : {len(v)}")
    #     for row in v:
    #         if 'ZL 202122371892.4' in row:
    #             switch = True
    #         if switch and window_len > 0:
    #             print(f"----{row}")
    #             window_len -= 1
    #         else:
    #             switch = False
            
    #     break
    
    # 二、解析数据行 -> 字典格式
    record:dict[str, list[dict[str, str]]] = {}
    for sub_event, row_data in origin_record.items():
        if "生效" in sub_event:
            record[sub_event] = []   
            r:dict[str, str] = {}
            for i, row in enumerate(row_data):
                # print(f"----debug: row = {row}")
                for j, element in enumerate(row):
                    if element in KEYWORD_METADATA:
                        assert element not in r, f"----debug: {r} 中存在重复的关键字元素：{element} 对应的行：{i} = {row}"
                        r[element] = ''
                    else:
                        if '-----------' in element or i == len(row_data)-1:
                            # print(f"----debug: r = {r}")
                            assert sum(1 for value in r.values() if value) >= 10, f"----debug: row = {row} 解析的数据记录存在缺失值：{r}"
                            assert len(r)==11 or len(r)==10, f"----debug: 解析的数据记录格式错误：原始数据={row_data[i]}\n\n  解析后数据={r}"
                            record[sub_event].append(r.copy())
                            # print(f"----debug: record[{sub_event}] = {record[sub_event]}")
                            r.clear()   # 清空临时记录变量
                        elif 'VK_PLACEHOLDER' in element:
                            continue
                        else:
                            # print(f"----debug: 处理前[list(r.keys())[-1]]={list(r.keys())[-1]} element={element}")
                            r[list(r.keys())[-1]] += element
                            # print(f"----debug: 处理后r{[list(r.keys())[-1]]} = {r[list(r.keys())[-1]]} ")
        else:
            continue  # 暂时不再处理其他子事件
    
    # 补遗1：最终数据记录中，发明名称中如有空格（且在pdf中有被识别为图片的生僻字），用*代替
    # print(f"----debug: RARE_CHARACTER_IMAGE_FLAG = {RARE_CHARACTER_IMAGE_FLAG}")
    if RARE_CHARACTER_IMAGE_FLAG:
        for sub_event, row_data in record.items():
            for i, row in enumerate(row_data):
                if '发明名称' in row:
                    if ' ' in row['发明名称']:
                        row['发明名称'] = row['发明名称'].replace(' ', '*')
                        record[sub_event][i] = row.copy()
                        print(f"----debug: 因为存在生僻字图片而进行的发明名称修正：{row['发明名称']}")
    
    # 补遗2：最终数据记录中，授权公告日若为空，则置为“无”
    for sub_event, row_data in record.items():
        for i, row in enumerate(row_data):
            if '授权公告日' not in row or row['授权公告日'] == '':
                row['授权公告日'] = '无'
                record[sub_event][i] = row.copy()
                # print(f"----debug: 因为授权公告日为空而进行的修正：{row['授权公告日']}")
            elif '申请公布日' in row and (row['申请公布日'] == '' or row['申请公布日'] is None):
                row['申请公布日'] = '无'
                record[sub_event][i] = row.copy()
                # print(f"----debug: 因为申请公布日为空而进行的修正：{row['申请公布日']}")
                      
    # debug:
    # print(f"\n---- 获取的PDF最终数据记录 : {record.keys()}")
    # {print(f"----{k} = {len(v)}") for k,v in record.items()}
    # for k,v in record.items():
    #     print(f"\n----{k} : {len(v)}")
    #     for row in v:
    #         print(f"----{row}")

    
    return record


def date_to_PDF_name(date: str, patent_type: str, event_type: str) -> str:
    '''
    根据公报日期，生成该日期的公报PDF文件名.
    版本：2024.12.31
    '''
    assert patent_type in ('FM', 'WG', 'XX')
    assert event_type in ('SW', 'SY', 'BFWXSW', 'BMSQSW', 'GZSW', 'HY', 'HYSY')

    year = int(date[0:4]) 
    month = int(date[5:7])
    day = int(date[8:10])    

    #本年度首日是周几？
    s0 = datetime.date(year, 1, 1).isocalendar()[2]

    #周数
    #专利局特殊记周法和通用计周法的调整因子N=1或N=0
    if s0 == 5:
        N = 1
    else:
        N = 0

    #本周是第几周
    num_weeks_real = datetime.date(year, month, day).isocalendar()[1] + N

    #周几
    what_day = datetime.date(year, month, day).isocalendar()[2]
    # print(f"year={year} month={month} day={day} s0={s0} num_weeks_real={num_weeks_real} what_day={what_day}")

    # 首年特殊调整（2021-01-01）
    if N == 1 and num_weeks_real == 54:
        num_weeks_real = 1
        what_day = 2
        print(f"首年调整：num_weeks_real={num_weeks_real} what_day={what_day}")
    elif s0 in (3, 4) and num_weeks_real == 1 and what_day == 5:  # 2020-01-03 周五,但首次更新
        what_day = 2
        print(f"首年调整情形2：num_weeks_real={num_weeks_real} what_day={what_day}")
    elif num_weeks_real == 1 and what_day == 2 and (month == 12 and day == 31):  #  2019-12-31 2020年首周,但日期属于2019年末尾
        num_weeks_real = 53
        print(f"首年调整情形3：{year}-{month}-{day} , num_weeks_real={num_weeks_real} what_day={what_day}")

    #生成文件名
    if what_day == 2:
        file_name = f"{patent_type}{event_type}{year- 1985 + 1}{num_weeks_real:02d}01.pdf"
    elif what_day == 5:
        file_name = f"{patent_type}{event_type}{year- 1985 + 1}{num_weeks_real:02d}02.pdf"
    else:
        raise ValueError(f"传入的日期：{date} 不在周二或周五,非公报日期")

    return file_name


def read_license_data_from_excel(file_path: str) -> dict|None:
    '''
    从excel文件中读取专利许可合同备案数据
    '''
    keyword_in_sheetname = '专利实施许可合同备案'
    # 打开excel文件
    wb = openpyxl.load_workbook(file_path, read_only=True)
    # 选择包含指定关键词的所有sheet 
    sheets = [s for s in wb.sheetnames if keyword_in_sheetname in s]
    
    if len(sheets) == 0:
        wb.close()
        return None
    else: 
        # 获取所有sheets中的数据
        data = {}
        for sheet_name in sheets:
            data[sheet_name] = []
            ws = wb[sheet_name]
            for row in ws.rows: # type: ignore
                data[sheet_name].append([cell.value for cell in row])
        
        wb.close()
        return data


def check_rows_and_return_missing_data(patent_type:str, event_type:str, rows: list) -> dict[int, dict[str, str]]|None:
    '''检查行数据，返回异常数据

    :param patent_type: 专利类型，'FM' or 'WG' or 'XX'
    :param event_type: 事件类型，'SW' or 'SY' or 'BFWXSW' or 'BMSQSW' or 'GZSW' or 'HY' or 'HYSY'
    :param rows: 行数据，每行数据为list的元素
    :return:  返回异常数据，dict，key为行号，value为该行的dict：missing_data（key为列名，value为该列对应的单元格值）
    '''


    # 根据样例数据，定义正则表达式用于格式校验 
    pattern_dict = {
        'IPC(主分类)' : r'^[A-Z]{1}\d{2}[A-Z]{1}\s\d{1,3}/\d{1,5}$',
        '主分类号' : r'\d{2}-\d{2}',
        '合同备案号' : r'^[A-Z]{0,1}\d{13}$', 
        '专利号/专利申请号' : r'^(ZL )?(\d{8}|\d{12})\.[0-9X]{1}$',  # 未测试，如果通过的话，复制到下边两个正则表达式中
        '专利号' : r'^(ZL )?(\d{8}|\d{12})\.[0-9X]{1}$',
        '专利申请号' : r'^(\d{8}|\d{12})\.[0-9X]{1}$',
        '申请日' : r'^20|19\d{2}\.\d{2}\.\d{2}$',
        '让与人' : r'^.{2,100}$',
        '受让人' : r'^.{2,100}$',
        # '发明名称' : r'^\S{1,40}$',
        '发明名称' : r'^.{1,90}$',
        '实用新型名称' : r'^.{1,30}$',
        # '使用外观设计的产品名称' : r'^\S{1,40}$',
        '使用外观设计的产品名称' : r'^.{1,30}$',
        '申请公布日' : r'^20|19\d{2}\.\d{2}\.\d{2}$',
        '授权公告日' : r'(^20|19\d{2}\.\d{2}\.\d{2}$)|(无)|None',
        # '许可种类' : r'^[A-Za-z\u4e00-\u9fa5]{4,8}$',
        '许可种类' : r'^[\u4e00-\u9fa5]{3,4}([,;][\u4e00-\u9fa5]{3,4})*$',
        '备案日期' : r'^20|19\d{2}\.\d{2}\.\d{2}$',
        '解除日' : r'^20|19\d{2}\.\d{2}\.\d{2}$',
    }


    wrong_data_rows: dict[int, dict[str, str]] = {} # 格式错误数据，元素为dict，key为行号，value为该行的dict：missing_data（key为列名，value为该列对应的单元格值）
    missing_data: dict[str, str] = {}  # 缺失值dict，key为列名，value为缺失值

    for i, row in enumerate(rows):
        # print(f'正在处理第{i+1}行数据...')
        # 如果是标题行，用标题行的值初始化缺失值dict的key
        if i == 0:   # 标题行,初始化缺失值dict
            for j, cell in enumerate(row):
                missing_data[cell] = ''
            missing_data['assist_positioning_info'] = json.dumps({'apn': '', 'record_num': '', 'length_of_sheet': len(rows)})
            continue
        else:
            # 如果当前行有空值，则将当前行的数据添加到返回值list中            
            if any(cell is None for cell in row):
                missing_data = dict(zip(missing_data.keys(), row))
                missing_data['assist_positioning_info'] = json.dumps({'apn': rows[i-1][2], 'record_num': rows[i-1][1], 'length_of_sheet': len(rows)})
                if patent_type == 'FM':
                    # 如果missing_data中，是否除“授权公告日”对应键值为None外，其他键对应键值为均不为None
                    # if any(missing_data[key] is None for key in missing_data.keys() if key != '授权公告日'):   # 该判断的含义为：如果missing_data中除“授权公告日”对应键值为None外，其他键对应键值如果为None，则该行数据存在缺失
                    if any(missing_data[key] is None for key in missing_data.keys()):
                        print(f"--debug: 第{i+1}行数据存在缺失值：\n{missing_data}")
                        wrong_data_rows[i] = missing_data
                elif patent_type == 'WG':
                    wrong_data_rows[i] = missing_data
                elif patent_type == 'XX':
                    # 查看missing_data中，是否除“申请公布号”对应键值为None外，其他键对应键值为均不为None
                    # [print(missing_data[key])  for key in missing_data.keys() if key!= '申请公布日']
                    if any(missing_data[key] is None for key in missing_data.keys() if key != '申请公布日'):
                        wrong_data_rows[i] = missing_data   
                else:
                    raise ValueError(f'--未识别的专利类型：{patent_type}')
            else:
                pass
    
            # 遍历当前行的每个单元格，校验格式
            for j, cell in enumerate(row):
                # print(f'正在处理第{i+1}行第{j+1}列数据：{cell}')
                if cell is not None:
                    # 校验方法：
                    # 1、选取合适的正则表达式：如果当前列名在正则表达式字典中，则用样例数据中的正则表达式；否则报错。
                    # 2、用正则表达式匹配单元格值。
                    # 3、如果匹配失败，则将当前行的数据添加到返回值list中。
                    # 4、如果匹配成功，则继续下一个单元格的匹配。
                    if list(missing_data.keys())[j] in pattern_dict.keys():
                        pattern = pattern_dict[list(missing_data.keys())[j]]
                        # print(f'--正在处理第{i+1}行第{j+1}列数据：{cell}\n--正则表达式：{pattern}')
                    else:
                        raise ValueError(f'--数据中未找到 {list(missing_data.keys())[j]} 列名')
                    
                    if not re.match(pattern, str(cell)):
                        print(f'--正在处理第{i+1}行第{j+1}列数据：{cell}\n--格式不匹配，当前正则表达式：{pattern}')
                        missing_data = dict(zip(missing_data.keys(), row))
                        missing_data['assist_positioning_info'] = json.dumps({'apn': rows[i-1][2], 'record_num': rows[i-1][1], 'length_of_sheet': len(rows)}) 
                        wrong_data_rows[i] = missing_data
                    else:
                        continue                                 
    
    if len(wrong_data_rows) == 0:
        print('--数据格式校验通过！')
        return None
    else:
        return wrong_data_rows


def get_original_data_from_pdf(file_path: str) -> dict[str, dict[int, list[list[str]]]]|None:
    '''
    根据专利号和合同备案号，从pdf文件中获取原始信息
    '''
    
    # 首先判断pdf文件是否存在
    if not os.path.exists(file_path):
        raise ValueError(f'文件 {file_path} 不存在，不能继续处理')

    
    pages_info = {} 
    # 打开pdf文件
    with pdfplumber.open(file_path) as pdf:
        # 遍历pdf中的所有页
        # time_start  = time.time()
        begin = False     # 标记是否找到专利实施许可合同备案数据开始位置
        pdf.pages.reverse()  # 逆序遍历，从最后一页开始
        for i, page in enumerate(pdf.pages[:]):
            # print(f'----正在处理第{page.page_number}页:')
            sys.stdout.write(f'\r----正在处理第 {page.page_number} 页----')
            sys.stdout.flush()
            
            # 解析pdf页中的文本数据
            page_text = page.extract_text(
                                x_tolerance=3, 
                                x_tolerance_ratio=None, 
                                y_tolerance=3, 
                                layout=True,
                                x_density=7.25,  # 7.25,5.75
                                y_density=26,    # 13
                                line_dir_render=None, 
                                char_dir_render=None, 
                                        )
            
            # debug
            # if page.page_number == 311:
            #     print(page_text)
            #     break
            global  RARE_CHARACTER_IMAGE_FLAG      # 生僻字图片的标志变量
            if len(page.images) > 0:
                RARE_CHARACTER_IMAGE_FLAG = True
                # print(f'----debug:RARE_CHARACTER_IMAGE_FLAG = {RARE_CHARACTER_IMAGE_FLAG}')
                # 识别图片中的文字
                for image in page.images:
                    print(f'----page {page.page_number} 发现图片')      
            else:
                pass
                      
            
            # 定位到专利实施许可合同备案数据开始位置，获取完整相关页面数据
            if '专利实施许可合同备案的生效、变更及注销' in page_text:   
                begin = True
                # print(f'----发现专利实施许可合同备案的生效、变更及注销，page_num = {page.page_number}')
                page_text_after_format = page_text.split('\n')

                # page_text_after_format = [split_text(line) for line in page_text_after_format]

                page_text_after_format = [line.split(' ') for line in page_text_after_format]
                page_text_after_format = [[x for x in line if x!=''] for line in page_text_after_format]
                page_text_after_format = [line for line in page_text_after_format if len(line) > 0]
                pages_info[page.page_number] = page_text_after_format
            else:
                if begin is True:
                    # print(f'----发现专利实施许可合同备案数据结束，page_num = {page.page_number -1}')
                    break  
                else:
                    page.close()
                    continue
            
            # debug
            # if page.page_number == 311:
            #     [print(line) for line in page_text_after_format]
            #     break
            
            page.close()
    

    if len(pages_info) == 0:
        raise ValueError(f'未找到专利实施许可合同备案数据，请检查文件：{file_path} 的内容是否正确')    
        return None
    else:
        print(f'\n----共获取 {len(pages_info)}({pages_info.keys()}) 页原始数据')    
        pages_info = dict(sorted(pages_info.items()))

    # 对按页面原始信息进行按子事件分类：
    # print(f"原始页面数据：{pages_info.keys()}")
    original_data :dict[str, dict[int, list[list[str]]]] = {}      # 原始数据，元素为dict，key为子事件名称，
                                                                    # value为该子事件的原始数据按页的字典列表（key为页号，value为该页的原始数据 list）
    for page_num, page_data in pages_info.items():
        # print(f"\n----正在处理 {page_num} ：{len(page_data)}")
        
        # 发现页眉和页脚
        header_index = [i for i, line in enumerate(page_data) if '专利实施许可合同备案' in ''.join(line)]
        footer_index = [i for i, line in enumerate(page_data) if '专利权质押' in ''.join(line)]
        if header_index:
            header_index = header_index[0]
            if footer_index:
                footer_index = footer_index[0]
            else:
                footer_index = -1
            # 截取页眉和页脚
            page_data = page_data[header_index+1 : footer_index]
        else:
            raise ValueError(f'\n未找到页眉和页脚，请检查页面{page_num}数据')
        row_data = [line for line in page_data if len(line) > 0]  # 去掉空行

        # debug
        # [print(f'----{line}')  for line in row_data ]
        
        # 按子事件分类
        for i, line in enumerate(row_data):
            # print(f'----正在处理 {i} 行数据：{line}')
            if '专利实施许可合同备案'in''.join(line) and len(line)==1:
                original_data[line[0]] = {}
                original_data[line[0]][page_num] = []  # 初始化新子事件
                # print(f'----发现新子事件：{line[0]}')
            else:
                if page_num in original_data[list(original_data.keys())[-1]]:
                    original_data[list(original_data.keys())[-1]][page_num].append(line)  # 追加到最后一个子事件中
                else:
                    original_data[list(original_data.keys())[-1]][page_num] = []
                    original_data[list(original_data.keys())[-1]][page_num].append(line)  # 新建子事件并追加该行
        
        # debug:显示orginal_data中的事件详情
        # for event_name, event_data in original_data.items():
        #     print(f'----共获取 {event_name} 的 {len(event_data)} 页原始数据')
        #     for page_num, page_data in event_data.items():
        #         print(f'------{page_num} 页：{len(page_data)} 行')    
        #         for line in page_data:
        #             print(f'--------{line}')
    
    return original_data      
        
    
def inference_patent_type_and_event_type(file_path: str) -> tuple[str, str]:
    '''推断专利类型和法律事件类型
    
    根据生成的法律状态excel数据文件路径，推断专利类型（FM、WG、XX），再根据文件内容推断法律事件类型（SW、SY）
    
    :param file_path: excel文件路径
    :return: 专利类型（FM、WG、XX）和法律事件类型（SW、SY），其它类型暂未实现
    '''

    file_name = file_path.split('\\')[-1]
    patent_type_word = file_name.split('_')[0][:2]
    if patent_type_word == '发明':
        patent_type = 'FM'
    elif patent_type_word == '外观':
        patent_type = 'WG'
    elif patent_type_word == '实用':
        patent_type = 'XX'
    else:
        raise ValueError(f'无法识别的专利类型：{patent_type_word}')
    
    event_type_word = file_name.split('_')[0]

    if  '法律状态' in event_type_word:
        event_type = 'SW'
    elif '索引' in event_type_word: # 索引号事件
        event_type = 'SY'
    else:
        raise ValueError(f'无法识别的事件类型：{event_type_word}')

    # print(f'推断出patent_type={patent_type}，event_type={event_type}')


    return patent_type, event_type
    

def fix_missing_data(broken_data: dict[str, dict[int, dict[str, str]]], data_record_from_PDF: dict[str, list[dict[str, str]]]) -> dict[str, dict[int, dict[str, str]]]:
    '''
    根据原始信息，修复缺失数据
    ;param broken_data: 缺损数据，元素为dict，key为sheet名，value为缺失值dict[行号int，dict[列名，单元格值]]
    ;param data_record_from_PDF: 原始信息，元素为dict，key为子事件名称，value为该子事件的原始数据列表(元素为dict，key为列名，value为单元格值)
    ;return fixed_data_list: 修复后的数据，元素为dict，key为sheet名，value为修复后的数据按行的字典列表（key为行号，value为该行的dict[列名，单元格值]）
    '''
    # print(f'--正在处理缺损数据：')
    fixed_data:dict[str, dict[int, dict[str, str]]] = {}
    for k,v in broken_data.items():
        print(f'\n--正在处理sheet {k} 中的共 {len(v)} 行缺损数据...')

        fixed_data[k] = {}
        n = 1
        for index, missing_data in v.items():
            print(f'----{n}、正在处理excel文件中事件：{k} 中的第{index+1}行缺损数据：\n{missing_data}')
            assert 'assist_positioning_info' in missing_data, f'--{k} 子事件中缺损数据中未找到 assist_positioning_info 字段，请检查文件或逻辑'
            # print(f'data_record_from_PDF.keys()={data_record_from_PDF.keys()}')
            for sub_event_name, sub_event_data in data_record_from_PDF.items():
                if k.split('.')[-1] in sub_event_name:
                    # debug:
                    # print(
                    #     f"预览 {sub_event_name} 子事件的原始数据（来自PDF二次解析）：\n首3行：\n{sub_event_data[0]}\n{sub_event_data[1]}\n{sub_event_data[2]}\n……\n末3行：\n{sub_event_data[-3]}\n{sub_event_data[-2]}\n{sub_event_data[-1]}"
                    # )
                    # print(f"预览missing_data:\n{type(missing_data)}：{missing_data}\n")

                    assist_info = missing_data.get('assist_positioning_info')
                    if isinstance(assist_info, str):
                        try:
                            assist_info = json.loads(assist_info)
                        except json.JSONDecodeError:
                            raise ValueError(f'assist_positioning_info JSON解析错误: {assist_info}')
                    if not isinstance(assist_info, dict):
                        raise ValueError(f'assist_positioning_info 字段类型错误: {type(assist_info)}：{assist_info}')
                    length_of_sheet = assist_info.get('length_of_sheet')
                    if length_of_sheet is None:
                        raise ValueError(f'length_of_sheet 字段缺失: {assist_info}')
                    if not isinstance(length_of_sheet, int):
                        try:
                            length_of_sheet = int(length_of_sheet)
                        except Exception:
                            raise ValueError(f'length_of_sheet 字段类型错误: {length_of_sheet}')
                    if length_of_sheet - 1 > len(sub_event_data):
                        raise ValueError(
                            f'{k} 子事件中缺损数据中assist_positioning_info字段中的'
                            f'length_of_sheet:{length_of_sheet-1} '
                            f'超过原始数据长度:{len(sub_event_data)}，请检查文件或逻辑'
                        )
                    elif length_of_sheet - 1 == len(sub_event_data):
                        fixed_data[k][index] = sub_event_data[index-1]
                    elif length_of_sheet - 1 < len(sub_event_data):
                        print(f'！！！{k} 子事件中缺损数据中assist_positioning_info字段中的'
                              f'length_of_sheet:{length_of_sheet-1} '
                              f'小于原始数据长度:{len(sub_event_data)}，替换全部数据！！！')
                        for i, record in enumerate(sub_event_data):
                            fixed_data[k][i+1] = record
                        break
                    else:
                        raise ValueError(f'--未知错误:{missing_data}，请检查文件或逻辑')
                        
                    # debug:
                    # for i, record in enumerate(sub_event_data):
                    #     print(record['专利申请号'])
            
            print(f'------已将缺损数据修复为：\n{fixed_data[k][index]}\n')
            n += 1
            
        # 校验修复后的数据是否完整
        # debug:
        # print(f'----正在校验修复后的数据是否完整：')
        # {print(f'--------{k} : {v}') for k,v in fixed_data[k].items()}
        assert len(fixed_data[k]) >= len(v), f'--{k} 子事件中缺损数据（长度：{len(v)}）未得到完整修复（长度：{len(fixed_data[k])}），请检查逻辑'
  
    # 修复后的数据，元素中如有空格的话，去除空格
    # 白名单
    white_lists = ('GaN E/D集成器', 'RH 终点温度预测方法', 'LMD 近似熵', 'Marker Sex 1', 'LTE PS', 'Micro LED', 'USB KEY', 'IP over Ethernet', 'HTTP DOS')  # 

    for sheet_name, sheet_data in fixed_data.items():
        for index, row_data in sheet_data.items():
            for col_name, cell_value in row_data.items():
                if col_name == '发明名称' and isinstance(cell_value, str):  # 处理发明名称中的空格
                    if ' ' in cell_value and not any(white_list in cell_value for white_list in white_lists) and RARE_CHARACTER_IMAGE_FLAG is False:
                        print(f'------!!!发现 第{index+1}行 {col_name} 字段中的空格：--{cell_value}--!!!')
                        index_list_of_broken_rows = sorted(set([x for y in [[i-1,i,i+1] for i in broken_data[sheet_name].keys()] for x in y]))  # 缺损数据所在行号附近列表
                        if index in index_list_of_broken_rows:
                            print(f'------当前行{index+1}存在于缺损数据行及其附近：{index_list_of_broken_rows}中，进行修复')
                            
                            # for dev:收集白名单字符
                            # raise ValueError(f'！！！第{index+1}行 “{col_name}” 数据中存在未预期的空格：--{cell_value}--！！！')
                        
                            fixed_data[sheet_name][index][col_name] = cell_value.replace(' ', '')
                            print(f'------已将 {col_name} 字段中的空格修复为：--{fixed_data[sheet_name][index][col_name]}--')
                        else:
                            print(f'------当前行{index+1}不存在于缺损数据行及其附近：{index_list_of_broken_rows}中，保留原值')
                            pass
                            

    return fixed_data
   

def write_fixed_data_to_excel(file_path: str, fixed_data_list: dict[str, dict[int, dict[str, str]]]|None):
    '''
    将修复后的数据写入excel文件
    param file_path: excel文件路径
    param fixed_data_list: 修复后的数据，元素为dict，key为sheet名，value为修复后的数据按行的字典列表（key为行号，value为该行的dict[列名，单元格值]）
    '''
    # 新建一个excel文件
    # wb = openpyxl.Workbook()

    # # 打开excel文件
    wb = openpyxl.load_workbook(file_path)

    # 选择第一个sheet
    ws = wb.active
    # 遍历修复后的数据，逐一写入excel文件
    if fixed_data_list is None:
        raise ValueError('--未能获取到修复后的数据，请检查文件或逻辑')
    
    for sheet_name, sheet_data in fixed_data_list.items():
        # 从excel文件中获取sheet
        if sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            # print(f'--正在处理sheet {sheet_name} 中的数据：')
        else:
            raise ValueError(f'--未找到 {sheet_name} 对应的sheet，请检查文件或逻辑')

        #从当前sheet中获取列名
        col_names = [cell.value for cell in ws[1]]
        # print(f'----当前sheet的列名：{col_names}')

        for index, row_data in sheet_data.items():
            # print(f'----正在写入修复后数据的第 {index} 行数据：')
            # 逐一写入数据
            for col_name, cell_value in row_data.items():
                # print(f'--------正在写入excel文件中 {col_name} 列数据：{cell_value}')
                if col_name in col_names:
                    ws.cell(row=index+1, column=col_names.index(col_name)+1, value=cell_value) # type: ignore
                else:
                    if col_name in ('专利申请号','专利号'):
                        ws.cell(row=index+1, column=col_names.index('专利号/专利申请号')+1, value=cell_value) # type: ignore
                    elif col_name in ('实用新型名称', '使用外观设计的产品名称'):
                        if col_name == '实用新型名称' : 
                            try:
                                col_index = col_names.index('实用新型名称')
                            except ValueError:
                                col_index = col_names.index('发明名称')
                        if col_name == '使用外观设计的产品名称': 
                            col_index = col_names.index('使用外观设计的产品名称')
                        ws.cell(row=index+1, column=col_index+1, value=cell_value) # type: ignore
                    else:
                        raise ValueError(f'--未找到 {col_name} 对应的列名，请检查文件或逻辑')

    # 保存文件
    if TEST_MODE:
        print('--测试模式，不保存文件')
        # wb.save('test.xlsx')
        wb.close()
    else:
        wb.save(file_path)
        wb.close()
        print(f'--已保存文件：{file_path}')


def verify_and_repair_patent_license_data(excel_file_path: str , pdf_file_directory: str):
    """检测、修复法律状态excel文件中的“专利许可合同备案”数据

    读取excel文件中的数据，根据对应pdf文件中的信息，检测、修复excel文件中的数据，并将修复后的数据写回excel文件

    :param file_path: excel文件路径
    :param pdf_file_directory: pdf文件所在目录
    :return: None
    """    

    # print(f'-正在处理:{file_path}')
    patent_type, event_type = inference_patent_type_and_event_type(excel_file_path)
    
    # 读取excel文件中的数据
    data = read_license_data_from_excel(excel_file_path)

    # 检查数据行中是否有缺失数据
    # 如果存在缺损，则返回缺失数据
    broken_data: dict[str, dict[int, dict[str, str]]] = {}  # 缺失值dict，key为sheet名，value为缺失值dict[行号int，dict[列名，单元格值]]
    if data is None:
        print('-未找到包含关键词"专利实施许可合同备案"的sheet')
    else:
        # 遍历所有sheet，处理数据
        for sheet_name, sheet_data in data.items():
            # 检查数据行中是否有空值
            if "生效" in sheet_name :
                print(f'--发现目标sheet：{sheet_name}, 检查sheet中是否有缺损数据...')
                r = check_rows_and_return_missing_data(patent_type, event_type, sheet_data)
                if r:
                    broken_data[sheet_name] = r # type: ignore
                    print(f'--数据格式校验不通过，共发现问题数据 {len(broken_data[sheet_name])} 条')

                # if check_rows_and_return_missing_data(patent_type, event_type, sheet_data):
                #     broken_data[sheet_name] = check_rows_and_return_missing_data(patent_type, event_type, sheet_data) # type: ignore
                #     print(f'--数据格式校验不通过，共发现问题数据 {len(broken_data[sheet_name])} 条')
    
    # 如果存在缺失数据，则处理缺失数据
    if len(broken_data) > 0:
        # debug:
        # return   # 只检查，不修复开关

        # print(f'-当前文件共发现 {len(broken_data)} 行缺失数据：\n{broken_data}\n')

        # 从excel文件名中推断出专利类型和事件类型,生成pdf文件名，
        # 从pdf文件中获取专利许可合同备案部分的原始信息
        # 最后以该原始信息补充、修正错误数据
        # patent_type,event_type = inference_patent_type_and_event_type(file_path)
        pdf_file_name = date_to_PDF_name(excel_file_path.split('_')[-1][:-5], patent_type, event_type)
        # print(f'--推断出当前存在缺损的excel文件 {file_path} ,对应的PDF文件名为 {pdf_file_name}')
        # print(f'--在 {pdf_file_directory} 目录中查找 {pdf_file_name} 文件，获取原始信息...')
        pdf_file_path = pdf_file_directory + pdf_file_name
        print(f'\n--正在处理 {pdf_file_path} 文件，获取原始信息...')
        original_data = get_original_data_from_pdf(pdf_file_path)
        data_record = origin_data_to_record(original_data)
        if data_record is None:
            print(f'--未能从 {pdf_file_name} 中获取 专利许可合同备案 数据，请检查文件或逻辑')
        else:
            # 处理缺损数据
            fixed_data_list = fix_missing_data(broken_data, data_record)
            if fixed_data_list is None:
                print(f'--未能修复 {excel_file_path} 中的缺损数据，请检查文件或逻辑')
            else:
                # 回写数据到excel文件
                write_fixed_data_to_excel(excel_file_path, fixed_data_list)
                print(f'-- {excel_file_path} 已处理完毕')
    else:
        print('--该文件不存在缺损数据，无需处理')

def main():
    """主函数，处理指定目录下的所有Excel文件"""

    # 获取目标目录所有待处理excel文件列表
    # excel_file_directory = "./"  # 修复未定义变量
    file_list = glob.glob(os.path.join(excel_file_directory, '*法律状态_*.xlsx'))
    print(f'在 {excel_file_directory} 目录中发现 {len(file_list)} 个excel文件：')

    # 处理大量文件时: 用processed_file_list记录上次程序运行时已处理的文件，避免重复处理
    # with open('processed_file_list.txt', 'r', encoding='utf-8') as f:
    #     processed_file_list = f.readlines()
    #     processed_file_list = [line.strip().split('、 ')[-1] for line in processed_file_list]
    # file_list = [file_path for file_path in file_list if file_path not in processed_file_list]

    for i, excel_file_path in enumerate(file_list):
        # file_path = r'E:\\已写备份20240915\发明专利法律状态_2022-08-05.xlsx'
        
        print(f'\n {i+1}/{len(file_list)}、{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}： {excel_file_path}\n')
        # 调用verify_and_repair_patent_license_data函数，对每个Excel文件进行数据验证和修复
        verify_and_repair_patent_license_data(excel_file_path, pdf_file_directory)

        # 处理大量文件时: 检测修复后，记录已处理的文件名
        # with open('processed_file_list.txt', 'a', encoding='utf-8') as f:
        #     f.write(f'{i+1}、 {excel_file_path}' + '\n')

        # for dev: 测试模式，只处理一个文件
        # break 

if __name__ == '__main__': 
    # 设置测试模式
    TEST_MODE = False  # 关闭测试模式
    # TEST_MODE = True  # 设置测试模式为开启

    # 读取excel
    # excel_file_directory = r'E:\\已写备份20240927\\'
    # excel_file_directory = r'Z:\\存量数据\\Data from epub\\事务\\已写备份20241001\\'
    # excel_file_directory = r".\\data\\" 
    # 读取pdf
    # pdf_file_directory = r'Z:\\存量数据\\Data from epub\\事务\\已处理\\'
    # pdf_file_directory = r'D:\\坚果云\\我的坚果云\\工作（个人）\\SDIP\\基础数据获取\\Epub公开数据抽取\\专利事务公开PDF数据抽取\\已处理\\'
    # pdf_file_directory = ".\\"
    
    if TEST_MODE:
        excel_file_directory = r'.\\data\\'
        pdf_file_directory = r'.\\data\\'
        main()
    else:
        #  单文件处理参数模式: 用于与epub数据解析程序集成
        print(f'--正在从命令行获取参数...{sys.argv}')
        if len(sys.argv) > 2:
            excel_file_path = sys.argv[1]
            pdf_file_directory = sys.argv[2]
            print(f'--已从命令行获取参数:\n     excel_file_path = {excel_file_path}\n     pdf_file_directory = {pdf_file_directory}\n')
            verify_and_repair_patent_license_data(excel_file_path, pdf_file_directory)
        else:
            print('--命令行参数不足，请指定excel_file_path和pdf_file_directory')
            if TEST_MODE:
                main()
            else:
                sys.exit()
