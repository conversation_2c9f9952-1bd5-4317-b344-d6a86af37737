AS=as
CC="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe"
CCACHE_DIR=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
CCACHE_LOGFILE=D:\code\FASTAP~1\FIX_LI~1.ONE\ccache-93584.txt
CCCOM=$CC -o $TARGET -c $CFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CFILESUFFIX=.c
CPPDEFINES=['_WIN32_WINNT=0x0501', '__NUITKA_NO_ASSERT__', 'MS_WIN64', '_NUITKA_ONEFILE_MODE', '_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1']
CPPDEFPREFIX=-D
CPPDEFSUFFIX=
CPPPATH=['D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib', '.', 'D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build\\include', 'D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build\\static_src', 'D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd']
CPPSUFFIXES=['.c', '.C', '.cxx', '.cpp', '.c++', '.cc', '.h', '.H', '.hxx', '.hpp', '.hh', '.F', '.fpp', '.FPP', '.m', '.mm', '.S', '.spp', '.SPP', '.sx']
CXX="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe"
CXXCOM=$CXX -o $TARGET -c $CXXFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CXXFILESUFFIX=.cc
HOST_ARCH=x86_64
HOST_OS=win32
INCPREFIX=-I
INCSUFFIX=
LDMODULE=$SHLINK
LDMODULEFLAGS=$SHLINKFLAGS
LDMODULENAME=${LDMODULEPREFIX}$_get_ldmodule_stem${_LDMODULESUFFIX}
LDMODULEPREFIX=$SHLIBPREFIX
LDMODULESUFFIX=$SHLIBSUFFIX
LDMODULEVERSION=$SHLIBVERSION
LDMODULE_NOVERSION_SYMLINK=$_get_shlib_dir${LDMODULEPREFIX}$_get_ldmodule_stem${LDMODULESUFFIX}
LDMODULE_SONAME_SYMLINK=$_get_shlib_dir$_LDMODULESONAME
LIBDIRPREFIX=-L
LIBDIRSUFFIX=
LIBLINKPREFIX=-l
LIBLINKSUFFIX=
LIBPATH=[]
LIBPREFIX=lib
LIBPREFIXES=['$LIBPREFIX']
LIBS=[]
LIBSUFFIX=.a
LIBSUFFIXES=['$LIBSUFFIX']
LINK="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe"
LINKCOM=$LINK -o $TARGET $LINKFLAGS $__RPATH @".\@link_input.txt" $_LIBDIRFLAGS $_LIBFLAGS
OBJPREFIX=
OBJSUFFIX=.o
PLATFORM=win32
PROGPREFIX=
PROGSUFFIX=.exe
RC=windres
RCCOM=$RC $_CPPDEFFLAGS $RCINCFLAGS ${RCINCPREFIX} ${SOURCE.dir} $RCFLAGS -i $SOURCE -o $TARGET
RCINCFLAGS=${_concat(RCINCPREFIX, CPPPATH, RCINCSUFFIX, __env__, RDirs, TARGET, SOURCE, affect_signature=False)}
RCINCPREFIX=--include-dir 
RCINCSUFFIX=
RPATHPREFIX=-Wl,-rpath=
RPATHSUFFIX=
SHCC=$CC
SHCCCOM=$SHCC -o $TARGET -c $SHCFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHCXX=$CXX
SHCXXCOM=$SHCXX -o $TARGET -c $SHCXXFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHELL=C:\WINDOWS\System32\cmd.exe
SHLIBNAME=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${_SHLIBSUFFIX}
SHLIBPREFIX=
SHLIBSONAMEFLAGS=-Wl,-soname=$_SHLIBSONAME
SHLIBSUFFIX=.dll
SHLIB_NOVERSION_SYMLINK=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${SHLIBSUFFIX}
SHLIB_SONAME_SYMLINK=${_get_shlib_dir}$_SHLIBSONAME
SHLINK=$LINK
SHOBJPREFIX=$OBJPREFIX
SHOBJSUFFIX=.o
TARGET_ARCH=x86_64
TEMPFILEARGJOIN= 
TEMPFILEPREFIX=@
TOOLS=['mingw', 'gcc', 'g++', 'gnulink']
WINDOWSDEFPREFIX=
WINDOWSDEFSUFFIX=.def
gcc_mode=True
clang_mode=False
msvc_mode=False
mingw_mode=True
clangcl_mode=False
PATH=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin;D:\users\qwers\miniconda3\envs\cpquery_api;D:\users\qwers\miniconda3\envs\cpquery_api\Library\mingw-w64\bin;D:\users\qwers\miniconda3\envs\cpquery_api\Library\usr\bin;D:\users\qwers\miniconda3\envs\cpquery_api\Library\bin;D:\users\qwers\miniconda3\envs\cpquery_api\Scripts;D:\users\qwers\miniconda3\envs\cpquery_api\bin;C:\Program Files\ImageMagick-7.0.10-Q16-HDRI;D:\program\python37\Scripts;D:\program\python37;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;D:\Program Files (x86)\WinSCP;C:\Program Files\dotnet;D:\program\python37\Lib\site-packages;D:\program\python37\Lib\site-packages;D:\program\python37\Lib\site-packages\jupyterlab;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\Program Files\nodejs;D:\Program Files\IDM Computer Solutions\UFTP;D:\users\qwers\miniconda3\Library\bin;D:\users\qwers\miniconda3\Scripts;D:\users\qwers\miniconda3;D:\Program Files\Git\cmd;D:\Program Files\Tailscale;C:\Program Files (x86)\ZeroTier\One;D:\Program Files\Tesseract-OCR;C:\Users\<USER>\.pyenv\pyenv-win\bin;C:\Users\<USER>\.pyenv\pyenv-win\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\program\Microsoft VS Code\bin;D:\Program Files (x86)\Nmap;C:\Users\<USER>\AppData\Roaming\npm;D:\users\qwers\miniconda3;D:\users\qwers\miniconda3\Scripts;D:\users\qwers\miniconda3\Library\bin;D:\Program Files\JetBrains\PyCharm 2023.2\bin;.;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.11.**********-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
TARGET=D:\code\FASTAP~1\fix_license_data_err.exe
