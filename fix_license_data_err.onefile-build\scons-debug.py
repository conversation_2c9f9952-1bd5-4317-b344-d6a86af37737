# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\python.exe', '-W', 'ignore', 'D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build\\ONEFIL~1.SCO', '--jobs', '16', '--warn=no-deprecated', '--no-site-dir', 'result_exe=D:\\code\\FASTAP~1\\fix_license_data_err.exe', 'source_dir=.', 'debug_mode=false', 'trace_mode=false', 'onefile_splash_screen=false', 'nuitka_src=D:\\users\\qwers\\MINICO~1\\envs\\CPQUER~1\\Lib\\SITE-P~1\\nuitka\\build', 'python_version=3.11', 'python_prefix=D:\\users\\qwers\\miniconda3\\envs\\cpquery_api', 'experimental=', 'debug_modes=', 'no_deployment=', 'mingw_mode=true', 'console_mode=force', 'noelf_mode=true', 'anaconda_python=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64'],
    env={'ALLUSERSPROFILE': 'C:\\ProgramData', 'APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming', 'CHROME_CRASHPAD_PIPE_NAME': '\\\\.\\pipe\\crashpad_57060_QGWLPERTSQRVMXWE', 'CLIENTNAME': 'DESKTOP-DQW', 'CNIPA_CLIENT_HOME': 'C:\\Program Files (x86)\\CNIPAClient', 'COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files', 'COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files', 'COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files', 'COMPUTERNAME': 'IBM-R7000P', 'COMSPEC': 'C:\\WINDOWS\\system32\\cmd.exe', 'CONDA_EXE': 'D:\\users\\qwers\\miniconda3\\Scripts\\conda.exe', 'DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData', 'FPS_BROWSER_APP_PROFILE_STRING': 'Internet Explorer', 'FPS_BROWSER_USER_PROFILE_STRING': 'Default', 'GPU_FORCE_64BIT_PTR': '0', 'GPU_MAX_ALLOC_PERCENT': '100', 'GPU_MAX_HEAP_SIZE': '100', 'GPU_SINGLE_ALLOC_PERCENT': '100', 'GPU_USE_SYNC_OBJECTS': '1', 'HOMEDRIVE': 'C:', 'HOMEPATH': '\\Users\\qwers', 'LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local', 'LOGONSERVER': '\\\\IBM-R7000P', 'MAGICK_HOME': 'C:\\Program Files\\ImageMagick-7.0.10-Q16-HDRI', 'NUITKA_SITE_FILENAME': '91084:D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Lib\\site.py', 'NUMBER_OF_PROCESSORS': '16', 'NVTOOLSEXT_PATH': 'C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\', 'ONEDRIVE': 'C:\\Users\\<USER>\\OneDrive', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined', 'OS': 'Windows_NT', 'PATH': 'D:\\users\\qwers\\miniconda3\\envs\\cpquery_api;D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Library\\mingw-w64\\bin;D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Library\\usr\\bin;D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Library\\bin;D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Scripts;D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\bin;C:\\Program Files\\ImageMagick-7.0.10-Q16-HDRI;D:\\program\\python37\\Scripts;D:\\program\\python37;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;D:\\Program Files (x86)\\WinSCP;C:\\Program Files\\dotnet;D:\\program\\python37\\Lib\\site-packages;D:\\program\\python37\\Lib\\site-packages;D:\\program\\python37\\Lib\\site-packages\\jupyterlab;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;D:\\Program Files\\nodejs;D:\\Program Files\\IDM Computer Solutions\\UFTP;D:\\users\\qwers\\miniconda3\\Library\\bin;D:\\users\\qwers\\miniconda3\\Scripts;D:\\users\\qwers\\miniconda3;D:\\Program Files\\Git\\cmd;D:\\Program Files\\Tailscale;C:\\Program Files (x86)\\ZeroTier\\One;D:\\Program Files\\Tesseract-OCR;C:\\Users\\<USER>\\.pyenv\\pyenv-win\\bin;C:\\Users\\<USER>\\.pyenv\\pyenv-win\\shims;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\program\\Microsoft VS Code\\bin;D:\\Program Files (x86)\\Nmap;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\users\\qwers\\miniconda3;D:\\users\\qwers\\miniconda3\\Scripts;D:\\users\\qwers\\miniconda3\\Library\\bin;D:\\Program Files\\JetBrains\\PyCharm 2023.2\\bin;.;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.11.2025070101-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand', 'PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL', 'PROCESSOR_ARCHITECTURE': 'AMD64', 'PROCESSOR_IDENTIFIER': 'AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD', 'PROCESSOR_LEVEL': '23', 'PROCESSOR_REVISION': '6001', 'PROGRAMDATA': 'C:\\ProgramData', 'PROGRAMFILES': 'C:\\Program Files', 'PROGRAMFILES(X86)': 'C:\\Program Files (x86)', 'PROGRAMW6432': 'C:\\Program Files', 'PSMODULEPATH': 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules', 'PUBLIC': 'C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm 2023.2\\bin;', 'PYENV': 'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\', 'PYENV_HOME': 'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\', 'PYENV_ROOT': 'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\', 'PYTHONHASHSEED': '0', 'SESSIONNAME': 'RDP-Tcp#25', 'SKF_FILE_CONFIG': 'C:\\Users\\<USER>\\AppData\\Roaming\\koal\\CertHelper_std\\SKFFileStore', 'SSL_CERT_DIR': 'D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Library\\ssl\\certs', 'SSL_CERT_FILE': 'D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Library\\ssl\\cacert.pem', 'SYSTEMDRIVE': 'C:', 'SYSTEMROOT': 'C:\\WINDOWS', 'TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp', 'TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp', 'USERDOMAIN': 'IBM-R7000P', 'USERDOMAIN_ROAMINGPROFILE': 'IBM-R7000P', 'USERNAME': 'qwers', 'USERPROFILE': 'C:\\Users\\<USER>\\WINDOWS', 'WXDRIVE_START_ARGS': '--wxdrive-setting=0 --disable-gpu --disable-software-rasterizer --enable-features=NetworkServiceInProcess', 'TERM_PROGRAM': 'vscode', 'TERM_PROGRAM_VERSION': '1.102.0', 'LANG': 'zh_CN.UTF-8', 'COLORTERM': 'truecolor', 'PYTHON_BASIC_REPL': '1', 'CONDA_DEFAULT_ENV': 'cpquery_api', 'CONDA_PREFIX': 'D:\\users\\qwers\\miniconda3\\envs\\cpquery_api', 'CONDA_PROMPT_MODIFIER': '(cpquery_api) ', 'CONDA_PYTHON_EXE': 'D:\\users\\qwers\\miniconda3\\python.exe', 'CONDA_SHLVL': '1', 'PROMPT': '(cpquery_api) $P$G', '_CONDA_EXE': 'D:\\users\\qwers\\miniconda3\\Scripts\\conda.exe', '_CONDA_OLD_CHCP': '936', 'CONDA_ROOT': 'D:\\users\\qwers\\miniconda3', '_CONDA_ROOT': 'D:\\users\\qwers\\miniconda3', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.11.2025070101-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-18ec812acf146f44.txt', 'BUNDLED_DEBUGPY_PATH': 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.11.2025070101-win32-x64\\bundled\\libs\\debugpy', 'GIT_ASKPASS': 'd:\\program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh', 'VSCODE_GIT_ASKPASS_NODE': 'D:\\program\\Microsoft VS Code\\Code.exe', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'VSCODE_GIT_ASKPASS_MAIN': 'd:\\program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js', 'VSCODE_GIT_IPC_HANDLE': '\\\\.\\pipe\\vscode-git-1ce83de2d5-sock', 'VSCODE_INJECTION': '1', '__CONDA_OPENSSL_CERT_DIR_SET': '1', '__CONDA_OPENSSL_CERT_FILE_SET': '1', 'NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1', 'NUITKA_PYTHON_EXE_PATH': 'D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\python.exe', 'NUITKA_PACKAGE_DIR': 'D:\\users\\qwers\\miniconda3\\envs\\cpquery_api\\Lib\\site-packages\\nuitka', '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000', '_NUITKA_ONEFILE_TEMP_SPEC': '{TEMP}\\onefile_{PID}_{TIME}', '_NUITKA_ONEFILE_TEMP_BOOL': '1', '_NUITKA_ONEFILE_COMPRESSION_BOOL': '1', '_NUITKA_ONEFILE_BUILD_BOOL': '1', '_NUITKA_ONEFILE_ARCHIVE_BOOL': '0', '_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_ONEFILE_TEMP_SPEC,_NUITKA_ONEFILE_TEMP_BOOL,_NUITKA_ONEFILE_COMPRESSION_BOOL,_NUITKA_ONEFILE_BUILD_BOOL,_NUITKA_ONEFILE_ARCHIVE_BOOL,_NUITKA_BUILD_DEFINITIONS_CATALOG', 'NUITKA_QUIET': '0'},
    shell=False
)